// Developer Details component styles
.card {
  box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
  border: 0;
  border-radius: 1rem;
}

.symbol {
  .symbol-label {
    font-size: 1rem;
    font-weight: 600;
  }
}

.badge {
  &.badge-light-success {
    background-color: rgba(34, 197, 94, 0.1);
    color: #22c55e;
  }

  &.badge-light-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }

  &.badge-light-primary {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }

  &.badge-light-warning {
    background-color: rgba(251, 191, 36, 0.1);
    color: #fbbf24;
  }

  &.badge-light-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }
}

.btn {
  &.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
  }
}

.text-hover-primary:hover {
  color: #3b82f6 !important;
}

.separator {
  &.separator-dashed {
    border-bottom: 1px dashed #e1e5e9;
  }
}

.rotate {
  transition: transform 0.3s ease;
  
  &.collapsed {
    transform: rotate(-90deg);
  }
}

.collapsible {
  cursor: pointer;
  
  &:hover {
    color: #3b82f6;
  }
}

// Enhanced SweetAlert Styles for Arabic
:host ::ng-deep {
  .custom-swal-arabic {
    direction: rtl !important;
    text-align: right !important;

    .swal2-popup {
      direction: rtl !important;
      text-align: right !important;
      padding: 2rem !important;
      border-radius: 12px !important;
    }

    .swal2-title {
      font-family: 'Noto Kufi Arabic', sans-serif !important;
      font-size: 1.6rem !important;
      font-weight: 800 !important;
      text-align: center !important;
      color: #1e3a8a !important;
      margin-bottom: 1.5rem !important;
    }

    .swal2-html-container {
      direction: rtl !important;
      text-align: center !important;
      font-family: 'Hacen Liner Screen', sans-serif !important;
      font-size: 1rem !important;
      color: #374151 !important;
    }

    .swal2-actions {
      flex-direction: row-reverse !important;
      gap: 0.75rem !important;
      margin-top: 2rem !important;
      justify-content: center !important;

      .swal2-confirm,
      .swal2-cancel {
        font-family: 'Hacen Liner Screen', sans-serif !important;
        font-size: 1rem !important;
        font-weight: 600 !important;
        padding: 0.8rem 2rem !important;
        border-radius: 10px !important;
        min-width: 120px !important;
        transition: all 0.3s ease !important;
        border: none !important;

        &:hover {
          transform: translateY(-2px) !important;
        }

        &:active {
          transform: translateY(0) !important;
        }
      }

      .swal2-confirm {
        background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
        color: white !important;

        &:hover {
          background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%) !important;
          box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3) !important;
        }
      }

      .swal2-cancel {
        background: #6b7280 !important;
        color: white !important;

        &:hover {
          background: #4b5563 !important;
          box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3) !important;
        }
      }
    }
  }

  .custom-swal-english {
    .swal2-actions {
      .swal2-confirm,
      .swal2-cancel {
        font-weight: 600 !important;
        padding: 0.8rem 2rem !important;
        border-radius: 10px !important;
        transition: all 0.3s ease !important;

        &:hover {
          transform: translateY(-2px) !important;
        }
      }
    }
  }
}

// RTL Support
:host-context(html[lang="ar"]) {
  .card-title {
    text-align: right !important;

    h3 {
      font-family: 'Noto Kufi Arabic', sans-serif !important;
      color: #0D47A1 !important;
    }

    span {
      font-family: 'Hacen Liner Screen', sans-serif !important;
    }
  }

  .card-toolbar {
    .btn {
      font-family: 'Hacen Liner Screen', sans-serif !important;
    }
  }

  .d-flex.flex-wrap.gap-3 {
    direction: rtl !important;

    .btn {
      font-family: 'Hacen Liner Screen', sans-serif !important;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }

  .symbol-100px {
    width: 80px !important;
    height: 80px !important;
  }

  .fs-3 {
    font-size: 1.5rem !important;
  }
}
