// Developer Details component styles
.card {
  box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
  border: 0;
  border-radius: 1rem;
}

.symbol {
  .symbol-label {
    font-size: 1rem;
    font-weight: 600;
    margin-right: 47px;

  }
}

.badge {
  &.badge-light-success {
    background-color: rgba(34, 197, 94, 0.1);
    color: #22c55e;
  }

  &.badge-light-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }

  &.badge-light-primary {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }

  &.badge-light-warning {
    background-color: rgba(251, 191, 36, 0.1);
    color: #fbbf24;
  }

  &.badge-light-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }
}

.btn {
  &.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
  }
}

.text-hover-primary:hover {
  color: #3b82f6 !important;
}

.separator {
  &.separator-dashed {
    border-bottom: 1px dashed #e1e5e9;
  }
}

.rotate {
  transition: transform 0.3s ease;

  &.collapsed {
    transform: rotate(-90deg);
  }
}

.collapsible {
  cursor: pointer;

  &:hover {
    color: #3b82f6;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }

  .symbol-100px {
    width: 80px !important;
    height: 80px !important;
  }

  .fs-3 {
    font-size: 1.5rem !important;
  }
}

// RTL Support for Arabic
.rtl-layout {
  direction: rtl !important;
  text-align: right !important;

  // Enhanced Header Design for Arabic
  .developer-header-card {
    .card-header.rtl-header {
      padding: 2rem 2.5rem !important;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
      border-bottom: 2px solid #e5e7eb !important;

      .card-title {
        width: 100% !important;
        margin-bottom: 0 !important;

        .d-flex.align-items-center {
          width: 100% !important;
          justify-content: space-between !important;
          flex-direction: row-reverse !important;
          gap: 2rem !important;

          .title-section {
            flex: 1 !important;
            justify-content: flex-end !important;

            .header-icon {
              color: #0d47a1 !important;
              margin-left: 1.5rem !important;
              margin-right: 0 !important;
              font-size: 2.5rem !important;
            }

            .title-content {
              text-align: right !important;

              .page-title-custom {
                font-family: 'Noto Kufi Arabic', sans-serif !important;
                color: #0d47a1 !important;
                font-size: 1.8rem !important;
                font-weight: 800 !important;
                margin-bottom: 0.5rem !important;
                line-height: 1.3 !important;
              }

              .subtitle-text {
                font-family: 'Hacen Liner Screen', sans-serif !important;
                color: #64748b !important;
                font-size: 1rem !important;
                font-weight: 500 !important;
                display: block !important;
              }
            }
          }

          .card-toolbar {
            flex-shrink: 0 !important;

            .back-btn-rtl {
              background: linear-gradient(135deg, #0d47a1 0%, #1565c0 100%) !important;
              border: none !important;
              color: white !important;
              font-family: 'Hacen Liner Screen', sans-serif !important;
              font-size: 1rem !important;
              font-weight: 600 !important;
              padding: 0.8rem 1.5rem !important;
              border-radius: 10px !important;
              box-shadow: 0 4px 12px rgba(13, 71, 161, 0.3) !important;
              transition: all 0.3s ease !important;

              &:hover {
                background: linear-gradient(135deg, #1565c0 0%, #1976d2 100%) !important;
                transform: translateY(-2px) !important;
                box-shadow: 0 6px 16px rgba(13, 71, 161, 0.4) !important;
              }

              &:active {
                transform: translateY(0) !important;
              }

              i {
                font-size: 1rem !important;
                margin-right: 0.5rem !important;
                margin-left: 0 !important;
              }
            }
          }
        }
      }
    }
  }

  // Keep existing styles for other elements
  .card-header {
    .card-title {
      .d-flex.align-items-center {
        text-align: right !important;
      }
    }

    .card-toolbar {
      .btn-secondary {
        font-family: 'Hacen Liner Screen', sans-serif !important;
        font-size: 0.9rem !important;
      }
    }
  }

  .page-title-custom {
    margin-top: 5px !important;
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    color: #0d47a1 !important;
    font-size: 1.4rem !important;
  }

  .contact-info-title {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    color: #0d47a1 !important;
    font-size: 1.2rem !important;
    font-weight: 700 !important;
  }

  .contact-details {
    .contact-label {
      font-family: 'Hacen Liner Screen', sans-serif !important;
      font-weight: 600 !important;
      color: #374151 !important;
      font-size: 1rem !important;
      margin-bottom: 0.5rem !important;
    }

    .contact-value {
      font-family: 'Hacen Liner Screen', sans-serif !important;
      color: #6b7280 !important;
      font-size: 0.95rem !important;
      margin-bottom: 1rem !important;

      a {
        font-family: 'Hacen Liner Screen', sans-serif !important;
        color: #6b7280 !important;

        &:hover {
          color: #0d47a1 !important;
        }
      }
    }
  }

  .card {
    .card-body {
      .d-flex.flex-center.flex-column {
        text-align: center !important;

        .fs-3 {
          font-family: 'Noto Kufi Arabic', sans-serif !important;
          color: #0d47a1 !important;
          font-weight: 700 !important;
        }
      }
    }
  }

  .projects-title {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    color: #0d47a1 !important;
    font-weight: 700 !important;
  }

  .stat-label {
    font-family: 'Hacen Liner Screen', sans-serif !important;
    text-align: center !important;
  }

  .card-header {
    .card-title {
      text-align: right !important;

      .card-label {
        font-family: 'Noto Kufi Arabic', sans-serif !important;
        color: #0d47a1 !important;
      }

      .text-muted {
        font-family: 'Hacen Liner Screen', sans-serif !important;
        text-align: right !important;
      }
    }
  }

  .quick-actions-title {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
    color: #0d47a1 !important;
    font-weight: 700 !important;
  }

  .action-btn {
    font-family: 'Hacen Liner Screen', sans-serif !important;
    font-size: 0.9rem !important;
    padding: 0.6rem 1.2rem !important;
    border-radius: 8px !important;

    i {
      font-size: 0.9rem !important;
    }
  }

  .d-flex.flex-wrap.gap-3 {
    &.flex-row-reverse {
      flex-direction: row-reverse !important;
      justify-content: flex-start !important;
    }
  }

  .status-badge {
    font-family: 'Hacen Liner Screen', sans-serif !important;
    font-size: 0.85rem !important;
    padding: 0.5rem 1rem !important;
    border-radius: 6px !important;
    text-align: center !important;
  }
}
