<!-- User Details Content -->
<div class="container-fluid" [class.rtl-layout]="translationService.isRTL()">
  <!-- Header -->
  <div class="row mb-5">
    <div class="col-12">
      <div class="card">
        <div class="card-header" [class.arabic-header]="translationService.isRTL()">
          <div class="d-flex justify-content-between align-items-center w-100">
            <!-- Title Section -->
            <div class="d-flex align-items-center" [class.flex-row-reverse]="translationService.isRTL()">
              <i class="fas fa-user fs-1 header-icon" [class.me-4]="!translationService.isRTL()"
                [class.ms-4]="translationService.isRTL()"></i>
              <div [class.text-end]="translationService.isRTL()">
                <h2 class="mb-1 header-title"
                  [style.font-family]="translationService.isRTL() ? 'Not<PERSON>, sans-serif' : 'inherit'"
                  [style.color]="translationService.isRTL() ? '#1565C0' : 'inherit'">
                  {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.TITLE' | translate }}
                </h2>
                <p class="mb-0 header-subtitle text-muted"
                  [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                  {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.SUBTITLE' | translate }}
                </p>
              </div>
            </div>

            <!-- Back Button -->
            <div class="back-button-container">
              <button class="btn btn-outline-primary" (click)="goBack()"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                <i class="fas fa-arrow-right" [class.me-2]="!translationService.isRTL()"
                  [class.ms-2]="translationService.isRTL()" *ngIf="translationService.isRTL()"></i>
                <i class="fas fa-arrow-left" [class.me-2]="!translationService.isRTL()"
                  [class.ms-2]="translationService.isRTL()" *ngIf="!translationService.isRTL()"></i>
                {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.BACK_TO_USERS' | translate }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- User Profile Card -->
  <div class="row">
    <div class="col-xl-4">
      <div class="card mb-5 mb-xl-8">
        <div class="card-body pt-15">
          <!-- Profile Image -->
          <div class="d-flex flex-center flex-column mb-5">
            <div class="symbol symbol-100px symbol-circle mb-7">
              <img *ngIf="user?.image" [src]="user?.image" alt="User Image" class="w-40 h-30"
                style="object-fit: cover; border-radius: 10%" />

            </div>

            <!-- Name and Role -->
            <a class="fs-3 text-gray-800 text-hover-primary fw-bold mb-5">
              {{ user?.fullName }}
            </a>

            <!-- <div class="fs-5 fw-semibold text-muted mb-6">
              {{ user.role || 'User' }}
            </div> -->

            <!-- Status Badges -->
            <div class="d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2 gap-2">
              <span class="badge fs-7 fw-bold px-3 py-2 status-badge" [ngClass]="getStatusClass(user?.isActive)"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                {{ (getStatusText(user?.isActive)) | translate }}
              </span>
              <span class="badge fs-7 fw-bold px-3 py-2 status-badge" [ngClass]="getVerificationClass(user?.isVerified)"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                {{ (getVerificationText(user?.isVerified)) | translate }}
              </span>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="d-flex flex-stack fs-4 py-3">
            <div class="fw-bold contact-info-title"
              [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'"
              [style.color]="translationService.isRTL() ? '#0D47A1' : 'inherit'">
              {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.CONTACT_INFORMATION' | translate }}
            </div>
          </div>

          <div class="separator separator-dashed my-3"></div>

          <div id="kt_customer_view_details" class="collapse show">
            <div class="py-5 fs-6 contact-details">
              <!-- Email -->
              <div class="fw-bold mt-5 contact-label"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.EMAIL' | translate }}
              </div>
              <div class="text-gray-600 contact-value"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                <a [href]="'mailto:' + user?.email" class="text-gray-600 text-hover-primary">
                  {{ user?.email }}
                </a>
              </div>

              <!-- Phone -->
              <div class="fw-bold mt-5 contact-label"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.PHONE' | translate }}
              </div>
              <div class="text-gray-600 contact-value"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                <a [href]="'tel:' + user?.phone" class="text-gray-600 text-hover-primary">
                  {{ user?.phone }}
                </a>
              </div>

              <!-- User Type -->
              <div class="fw-bold mt-5 contact-label"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.USER_TYPE' | translate }}
              </div>
              <div class="text-gray-600 contact-value"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                <span class="badge fs-7 fw-bold px-3 py-2 mt-2" [ngClass]="getUserTypeClass(user?.role)">
                  {{ translationService.isRTL() ? getUserRoleText(user?.role) : user?.role }}
                </span>
              </div>

              <!-- Last Login -->
              <div class="fw-bold mt-5 contact-label" *ngIf="user?.lastLogin"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.LAST_LOGIN' | translate }}
              </div>
              <div class="text-gray-600 contact-value" *ngIf="user?.lastLogin"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                {{ user?.lastLogin | date:'medium' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Statistics and Overview -->
    <div class="col-xl-8">
      <!-- User Overview -->
      <div class="card mb-5 mb-xl-8">
        <div class="card-header border-0 pt-5">
          <h3 class="card-title align-items-start flex-column">
            <span class="card-label fw-bold fs-3 mb-1 projects-title"
              [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'"
              [style.color]="translationService.isRTL() ? '#0D47A1' : 'inherit'">
              {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.USER_OVERVIEW' | translate }}
            </span>
            <span class="text-muted mt-1 fw-semibold fs-7"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.USER_ACCOUNT_INFO' | translate }}
            </span>
          </h3>
        </div>

        <div class="card-body py-3">
          <div class="row g-6 g-xl-9">
            <!-- Account Status -->
            <div class="col-sm-6 col-xl-4">
              <div class="card h-100">
                <div class="card-body d-flex flex-center flex-column py-9 px-5">
                  <div class="symbol symbol-50px symbol-circle mb-5">
                    <span class="symbol-label" [ngClass]="user?.isActive ? 'bg-light-success' : 'bg-light-danger'">
                      <i class="fas"
                        [ngClass]="user?.isActive ? 'fa-check-circle text-success' : 'fa-times-circle text-danger'"
                        class="fs-2x"></i>
                    </span>
                  </div>
                  <div class="fs-4 fw-bold text-gray-800 mb-2">
                    {{ (getStatusText(user?.isActive)) | translate }}
                  </div>
                  <div class="fw-semibold text-gray-400 stat-label"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.ACCOUNT_STATUS' | translate }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Verification Status -->
            <div class="col-sm-6 col-xl-4">
              <div class="card h-100">
                <div class="card-body d-flex flex-center flex-column py-9 px-5">
                  <div class="symbol symbol-50px symbol-circle mb-5">
                    <span class="symbol-label" [ngClass]="user?.isVerified ? 'bg-light-success' : 'bg-light-warning'">
                      <i class="fas"
                        [ngClass]="user?.isVerified ? 'fa-shield-alt text-success' : 'fa-exclamation-triangle text-warning'"
                        class="fs-2x"></i>
                    </span>
                  </div>
                  <div class="fs-4 fw-bold text-gray-800 mb-2">
                    {{ (getVerificationText(user?.isVerified)) | translate }}
                  </div>
                  <div class="fw-semibold text-gray-400 stat-label"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.VERIFIED' | translate }}
                  </div>
                </div>
              </div>
            </div>

            <!-- User Type -->
            <div class="col-sm-6 col-xl-4">
              <div class="card h-100">
                <div class="card-body d-flex flex-center flex-column py-9 px-5">
                  <div class="symbol symbol-50px symbol-circle mb-5">
                    <span class="symbol-label bg-light-primary">
                      <i class="fas fa-user-tag text-primary fs-2x"></i>
                    </span>
                  </div>
                  <div class="fs-4 fw-bold text-gray-800 mb-2">{{ translationService.isRTL() ?
                    getUserRoleText(user?.role) : (user?.role || 'N/A') }}</div>
                  <div class="fw-semibold text-gray-400 stat-label"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.USER_TYPE' | translate }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="card">
        <div class="card-header border-0 pt-5">
          <h3 class="card-title align-items-start flex-column">
            <span class="card-label fw-bold fs-3 mb-1 quick-actions-title"
              [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'"
              [style.color]="translationService.isRTL() ? '#0D47A1' : 'inherit'">
              {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.QUICK_ACTIONS' | translate }}
            </span>
            <span class="text-muted mt-1 fw-semibold fs-7"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.MANAGE_ACCOUNT' | translate }}
            </span>
          </h3>
        </div>

        <div class="card-body py-3">
          <div class="d-flex flex-wrap gap-3" [class.flex-row-reverse]="translationService.isRTL()">
            <button class="btn btn-success btn-sm action-btn" *ngIf="!user?.isActive" (click)="toggleUserStatus()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fas fa-check" [class.me-2]="!translationService.isRTL()"
                [class.ms-2]="translationService.isRTL()"></i>
              {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.ACTIVATE_ACCOUNT' | translate }}
            </button>

            <button class="btn btn-warning btn-sm action-btn" *ngIf="user?.isActive" (click)="toggleUserStatus()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fas fa-pause" [class.me-2]="!translationService.isRTL()"
                [class.ms-2]="translationService.isRTL()"></i>
              {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.SUSPEND_ACCOUNT' | translate }}
            </button>

            <a class="btn btn-primary btn-sm action-btn" [routerLink]="['/chat']"
              [queryParams]="{ chatWithUID: user?.id }"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fas fa-envelope" [class.me-2]="!translationService.isRTL()"
                [class.ms-2]="translationService.isRTL()"></i>
              {{ 'SUPER_ADMIN.ALL_USERS.USER_DETAILS.SEND_MESSAGE' | translate }}
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>