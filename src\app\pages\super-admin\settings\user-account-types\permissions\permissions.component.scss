// Permissions component styling

// Enhanced card styling
.card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #e1e3ea;
  margin-bottom: 25px;
  transition: all 0.3s ease;
}

// Custom Permissions Header
.custom-permissions-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #e1e3ea;
  padding: 25px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 90px;

  .header-content-wrapper {
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
  }

  .header-icon-section {
    .custom-icon-container {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #198754 0%, #20c997 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(25, 135, 84, 0.3);

      i {
        font-size: 1.8rem;
        color: white;
      }
    }
  }

  .header-text-section {
    flex: 1;

    .header-title {
      font-size: 1.75rem;
      font-weight: 700;
      color: #2d3748;
      margin: 0 0 5px 0;
      line-height: 1.3;
    }

    .header-subtitle {
      font-size: 1rem;
      color: #718096;
      font-weight: 500;
    }
  }

  // Arabic layout
  &.arabic-header {
    direction: rtl;

    .header-content-wrapper {
      justify-content: flex-start;
      text-align: right;

      // When icon is hidden, center the text
      .header-text-section {
        flex: none;
      }
    }

    .header-text-section {
      text-align: right;

      .header-title {
        font-family: 'Noto Kufi Arabic', sans-serif;
        font-size: 2rem;
        margin-bottom: 8px;
      }

      .header-subtitle {
        font-family: 'Hacen Liner Screen', sans-serif;
        font-size: 1.1rem;
      }
    }

    .card-toolbar {
      direction: ltr;

      .btn {
        font-family: 'Hacen Liner Screen', sans-serif;
      }
    }
  }
}

// Custom Table Header
.custom-table-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e1e3ea;
  padding: 20px 25px;

  .table-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .table-header-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
  }

  // Arabic layout
  &.arabic-table-header {
    direction: rtl;
    text-align: right;

    .table-header-content {
      justify-content: flex-start;
    }

    .table-header-title {
      font-family: 'Noto Kufi Arabic', sans-serif;
      font-size: 1.6rem;
      text-align: right;
    }
  }
}

// Old card-header styles removed to avoid conflicts

// Table improvements
.table {
  margin-bottom: 0;

  thead th {
    border-bottom: 2px solid #e1e3ea;
    padding: 1rem 0.75rem;
    font-weight: 600;
    color: #5e6278;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  tbody td {
    padding: 1rem 0.75rem;
    border-bottom: 1px solid #f1f1f2;
    vertical-align: middle;
  }

  tbody tr:hover {
    background-color: #f8f9fa;
  }
}

// Symbol styling
.symbol {
  &.symbol-45px {
    width: 45px;
    height: 45px;

    .symbol-label {
      border-radius: 8px;

      i {
        font-size: 1.2rem;
      }
    }
  }
}

// Search input styling
.form-control {
  border-radius: 8px;
  border: 1px solid #e1e3ea;

  &:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }

  &.form-control-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
}

// Empty state styling
.text-center.py-5 {
  padding: 3rem 1rem !important;

  .fas {
    color: #a1a5b7;
    margin-bottom: 1rem;
  }

  .text-muted {
    font-size: 1rem;
    color: #a1a5b7 !important;
  }
}

// Arabic specific adjustments
html[lang="ar"] {
  .custom-permissions-header {
    .header-content-wrapper {
      justify-content: center !important;

      .header-text-section {
        text-align: center !important;

        .header-title {
          text-align: center !important;
        }

        .header-subtitle {
          text-align: center !important;
        }
      }
    }
  }

  // Search input without icon in Arabic
  .position-relative {
    .form-control {
      padding-left: 0.75rem !important;
      padding-right: 0.75rem !important;
    }
  }

  // Enhanced Arabic Empty State
  .custom-empty-state.arabic-empty-state {
    padding: 5rem 2rem !important;

    .empty-state-content {
      .empty-state-title {
        background: linear-gradient(135deg, #495057 0%, #6c757d 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: none;
      }

      .empty-state-subtitle {
        color: #8a8a8a !important;
        font-weight: 500;
      }
    }
  }

  // Arabic permission details stacking
  .permission-details {
    .fw-bold,
    .text-primary,
    .text-muted {
      display: block !important;
      width: 100% !important;
    }

    .fw-bold {
      margin-bottom: 0.5rem !important;
    }

    .text-primary {
      margin-bottom: 0.3rem !important;
    }

    .text-muted {
      margin-top: 0.2rem !important;
    }
  }
}

// Old RTL styles removed - using custom headers now

// Enhanced table styling
.table-hover tbody tr:hover {
  background-color: #f8f9fa !important;
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

.table-rounded {
  border-radius: 12px;
  overflow: hidden;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}

// Enhanced symbol styling
.symbol-label {
  transition: all 0.3s ease;

  &.bg-light-primary {
    background-color: rgba(13, 110, 253, 0.1) !important;

    &:hover {
      background-color: rgba(13, 110, 253, 0.2) !important;
      transform: scale(1.05);
    }
  }

  &.bg-light-success {
    background-color: rgba(25, 135, 84, 0.1) !important;

    &:hover {
      background-color: rgba(25, 135, 84, 0.2) !important;
      transform: scale(1.05);
    }
  }
}

// Custom Empty State
.custom-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;

  .empty-state-icon {
    margin-bottom: 2rem;

    .symbol-100px {
      width: 100px;
      height: 100px;

      .symbol-label {
        border-radius: 50%;

        i {
          font-size: 2.5rem;
        }
      }
    }
  }

  .empty-state-content {
    max-width: 400px;

    .empty-state-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #6c757d;
      margin-bottom: 0.75rem;
      line-height: 1.4;
    }

    .empty-state-subtitle {
      font-size: 1rem;
      color: #a1a5b7;
      line-height: 1.5;
    }
  }

  // Arabic layout
  &.arabic-empty-state {
    text-align: center;

    .empty-state-content {
      .empty-state-title {
        font-family: 'Noto Kufi Arabic', sans-serif;
        font-size: 1.5rem;
        font-weight: 700;
        color: #495057;
        margin-bottom: 1rem;
      }

      .empty-state-subtitle {
        font-family: 'Hacen Liner Screen', sans-serif;
        font-size: 1.1rem;
        color: #6c757d;
        line-height: 1.6;
      }
    }
  }
}

// Enhanced empty state fallback
.py-10 {
  padding: 4rem 2rem !important;
}

// Component-specific RTL support - Arabic Language Only
:host-context(html[lang="ar"]) {
  .d-flex.justify-content-center {
    direction: ltr; // Keep pagination in LTR
  }

  // Complete Arabic layout fix
  .table {
    direction: rtl !important;
    text-align: right !important;

    th, td {
      text-align: right !important;
      direction: rtl !important;

      &.ps-4 {
        padding-right: 1.5rem !important;
        padding-left: 0.75rem !important;
      }
    }

    // Fix table row layout
    tbody tr {
      direction: rtl !important;

      td {
        &:first-child {
          text-align: right !important;

          .d-flex {
            // flex-direction: row !important;
            justify-content: flex-start !important;
            text-align: right !important;

            .symbol {
              order: 1;
              margin-left: 0 !important;
              margin-right: 1rem !important;
            }

            .d-flex.flex-column {
              order: 2;
              text-align: right !important;
              flex: 1;

              div {
                text-align: right !important;
              }
            }
          }
        }

        &:last-child {
          text-align: right !important;

          .d-flex.flex-column {
            text-align: right !important;

            span {
              text-align: right !important;
            }
          }
        }
      }
    }
  }

  // RTL empty state
  .py-10 {
    .d-flex.flex-column {
      text-align: center !important;
    }
  }

  // Fix card header in Arabic
  .card-header {
    direction: rtl !important;

    .card-title {
      .d-flex.align-items-center {
        flex-direction: row !important;
        text-align: right !important;

        .symbol {
          order: 1;
          margin-left: 0 !important;
          margin-right: 1rem !important;
        }

        div {
          order: 2;
          text-align: right !important;
          flex: 1;
        }
      }
    }

    .card-toolbar {
      order: 1;

      .d-flex {
        flex-direction: row-reverse !important;

        .btn {
          margin-left: 0.5rem !important;
          margin-right: 0 !important;

          &:first-child {
            margin-left: 0 !important;
          }
        }
      }
    }
  }

  // Fix search section
  .card-header {
    .card-toolbar {
      .d-flex.align-items-center {
        flex-direction: row-reverse !important;

        .position-relative {
          direction: rtl !important;

          .form-control {
            text-align: right !important;
            direction: rtl !important;
            padding-right: 2.5rem !important;
            padding-left: 0.75rem !important;
          }

          .fas.fa-search {
            right: 0.75rem !important;
            left: auto !important;
          }
        }
      }
    }
  }
}

// Force Arabic layout fixes
html[lang="ar"] {
  app-permissions {
    .container-fluid {
      direction: rtl !important;
    }

    .card {
      direction: rtl !important;
      text-align: right !important;
    }

    .table-responsive {
      direction: rtl !important;

      .table {
        direction: rtl !important;

        thead th {
          text-align: right !important;
          font-family: 'Noto Kufi Arabic', sans-serif !important;
        }

        tbody td {
          text-align: right !important;
          font-family: 'Hacen Liner Screen', sans-serif !important;

          .d-flex {
            justify-content: flex-start !important;
            text-align: right !important;

            .symbol + .d-flex {
              margin-right: 1rem !important;
              margin-left: 0 !important;
            }
          }
        }
      }
    }

    // Fix specific permission row layout
    .table tbody tr td:first-child {
      .d-flex.align-items-center {
        display: flex !important;
        flex-direction: row !important;
        align-items: center !important;
        justify-content: flex-start !important;
        text-align: right !important;
        gap: 1rem !important;

        .symbol {
          flex-shrink: 0 !important;
          margin: 0 !important;
        }

        .d-flex.flex-column {
          flex: 1 !important;
          text-align: right !important;

          * {
            text-align: right !important;
          }
        }
      }
    }

    // Fix date column
    .table tbody tr td:last-child {
      text-align: right !important;

      .d-flex.flex-column {
        text-align: right !important;

        span {
          text-align: right !important;
        }
      }
    }
  }
}

// Enhanced SweetAlert Styles for Arabic
:host ::ng-deep {
  .custom-swal-arabic {
    direction: rtl !important;
    text-align: right !important;
    min-width: 400px !important;
    max-width: 500px !important;

    .swal2-popup {
      direction: rtl !important;
      text-align: right !important;
      padding: 2rem !important;
      border-radius: 12px !important;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    }

    .swal2-title {
      font-family: 'Noto Kufi Arabic', sans-serif !important;
      font-size: 1.6rem !important;
      font-weight: 800 !important;
      text-align: center !important;
      color: #1e3a8a !important;
      margin-bottom: 1.5rem !important;
      line-height: 1.4 !important;
    }

    .swal2-html-container {
      direction: rtl !important;
      text-align: right !important;
      margin: 0 !important;
      padding: 0 !important;

      > div {
        direction: rtl !important;
        text-align: right !important;
      }

      .swal2-input,
      .swal2-textarea {
        font-family: 'Hacen Liner Screen', sans-serif !important;
        direction: rtl !important;
        text-align: right !important;
        border: 2px solid #e5e7eb !important;
        border-radius: 10px !important;
        padding: 0.9rem 1rem !important;
        font-size: 1rem !important;
        width: 100% !important;
        margin-bottom: 1rem !important;
        background: #f8fafc !important;
        transition: all 0.3s ease !important;

        &:focus {
          border-color: #1e3a8a !important;
          box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25) !important;
          background: white !important;
          outline: none !important;
        }

        &::placeholder {
          color: #9ca3af !important;
          font-family: 'Hacen Liner Screen', sans-serif !important;
          text-align: right !important;
        }
      }

      .swal2-textarea {
        min-height: 100px !important;
        resize: vertical !important;
      }
    }

    .swal2-actions {
      flex-direction: row-reverse !important;
      gap: 0.75rem !important;
      margin-top: 2rem !important;
      justify-content: center !important;

      .btn {
        font-family: 'Hacen Liner Screen', sans-serif !important;
        font-size: 1rem !important;
        font-weight: 600 !important;
        padding: 0.8rem 2rem !important;
        border-radius: 10px !important;
        min-width: 120px !important;
        transition: all 0.3s ease !important;
        border: none !important;

        &.btn-primary {
          background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
          color: white !important;

          &:hover {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3) !important;
          }

          &:active {
            transform: translateY(0) !important;
          }
        }

        &.btn-secondary {
          background: #6b7280 !important;
          color: white !important;

          &:hover {
            background: #4b5563 !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3) !important;
          }

          &:active {
            transform: translateY(0) !important;
          }
        }
      }
    }

    .swal2-validation-message {
      font-family: 'Hacen Liner Screen', sans-serif !important;
      text-align: center !important;
      direction: rtl !important;
      color: #dc2626 !important;
      font-size: 0.9rem !important;
      margin-top: 0.5rem !important;
      padding: 0.5rem !important;
      background: rgba(220, 38, 38, 0.1) !important;
      border-radius: 6px !important;
    }

    /* Validation message already defined above */
  }

  .custom-swal-english {
    .swal2-actions {
      .btn {
        font-weight: 600 !important;
        padding: 0.75rem 1.5rem !important;
        border-radius: 8px !important;

        &.btn-primary {
          background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%) !important;
          border: none !important;

          &:hover {
            background: linear-gradient(135deg, #16a34a 0%, #15803d 100%) !important;
            transform: translateY(-1px) !important;
          }
        }

        &.btn-secondary {
          background: #6b7280 !important;
          border: none !important;
          color: white !important;

          &:hover {
            background: #4b5563 !important;
            transform: translateY(-1px) !important;
          }
        }
      }
    }
  }
}

// Specific layout classes for better control
.permission-row-layout {
  display: flex !important;
  align-items: center !important;
  gap: 1rem !important;

  .permission-icon {
    flex-shrink: 0 !important;
  }

  .permission-details {
    flex: 1 !important;
  }

  &.arabic-layout {
    flex-direction: row !important;
    justify-content: flex-start !important;
    text-align: right !important;

    .permission-icon {
      order: 1;
    }

    .permission-details {
      order: 2;
      text-align: right !important;

      * {
        text-align: right !important;
      }
    }
  }
}

.permission-date-column {
  .d-flex.flex-column {
    align-items: flex-start !important;

    span {
      display: block !important;
    }
  }
}

// Arabic specific fixes - Match exact design
html[lang="ar"] {
  .permission-row-layout.arabic-layout {
    direction: ltr !important; // Keep LTR for icon-text layout
    justify-content: flex-start !important;
    align-items: center !important;
    padding: 1rem !important;
    background: #f8f9fa !important;
    border-radius: 8px !important;
    margin-bottom: 0.5rem !important;
    gap: 1rem !important;

    .permission-icon {
      display: flex !important; // Show icon like in the image
      flex-shrink: 0 !important;
      order: 1 !important;

      .symbol-label {
        background: #e3f2fd !important;
        border-radius: 8px !important;

        i {
          color: #1976d2 !important;
          font-size: 1.2rem !important;
        }
      }
    }

    .permission-details {
      direction: rtl !important;
      text-align: right !important;
      flex: 1 !important;
      order: 2 !important;

      // Permission name (like "view_user") - First line
      .text-primary {
        font-family: 'Hacen Liner Screen', sans-serif !important;
        font-size: 1rem !important;
        font-weight: 600 !important;
        color: #1976d2 !important;
        margin-bottom: 0.1rem !important;
        line-height: 1.3 !important;
        text-align: right !important;
        display: block !important;
        width: 100% !important;
        order: 1 !important;
      }

      // Main title (display name) - Second line
      .fw-bold {
        font-family: 'Noto Kufi Arabic', sans-serif !important;
        font-size: 0.9rem !important;
        font-weight: 500 !important;
        color: #9e9e9e !important;
        margin-bottom: 0.1rem !important;
        line-height: 1.4 !important;
        text-align: right !important;
        display: block !important;
        width: 100% !important;
        order: 2 !important;
      }

      // Description styling
      .text-muted {
        font-family: 'Hacen Liner Screen', sans-serif !important;
        font-size: 0.9rem !important;
        font-weight: 400 !important;
        color: #9e9e9e !important;
        margin-top: 0.25rem !important;
        line-height: 1.5 !important;
        text-align: right !important;
        display: block !important;
        width: 100% !important;
        order: 3 !important;
      }
    }
  }

  .permission-date-column {
    direction: rtl !important;
    text-align: right !important;
    padding: 1.5rem 1rem !important;

    .d-flex.flex-column {
      align-items: flex-end !important;
      text-align: right !important;
      background: rgba(108, 117, 125, 0.1) !important;
      padding: 0.75rem !important;
      border-radius: 6px !important;

      span {
        text-align: right !important;
        font-family: 'Hacen Liner Screen', sans-serif !important;

        &:first-child {
          font-size: 1rem !important;
          font-weight: 600 !important;
          color: #495057 !important;
          margin-bottom: 0.25rem !important;
        }

        &:last-child {
          font-size: 0.85rem !important;
          color: #6c757d !important;
        }
      }
    }
  }

  // Enhanced table styling for Arabic
  .table {
    tbody {
      tr {
        border-bottom: 2px solid #f1f3f4 !important;

        &:hover {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
          transform: translateY(-2px) !important;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        }

        td {
          border-bottom: none !important;
          vertical-align: middle !important;
        }
      }
    }
  }
} using Metronic design
.card {
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e3ea;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e1e3ea;
  padding: 1.5rem;

  .card-title {
    margin-bottom: 0;

    h3 {
      color: #2d3748;
      font-weight: 700;
    }
  }

  .card-toolbar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
}

.card-body {
  padding: 1.5rem;
}

// Table styling
.table {
  margin-bottom: 0;

  thead th {
    border-bottom: 2px solid #e1e3ea;
    padding: 1rem 0.75rem;
    font-weight: 600;
    color: #5e6278;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  tbody td {
    padding: 1rem 0.75rem;
    border-bottom: 1px solid #f1f1f2;
    vertical-align: middle;
  }

  tbody tr:hover {
    background-color: #f8f9fa;
  }
}

// Badge styling
.badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;

  &.badge-light-success {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
  }

  &.badge-light-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
  }

  &.badge-light-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
  }

  &.badge-light-primary {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
  }

  &.badge-light-info {
    background-color: rgba(13, 202, 240, 0.1);
    color: #0dcaf0;
  }

  &.badge-light-secondary {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
  }
}

// Button styling
.btn {
  border-radius: 8px;
  font-weight: 500;

  &.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  &.btn-light {
    background-color: #f8f9fa;
    border-color: #f8f9fa;
    color: #5e6278;

    &:hover, &.btn-active-light-primary:hover {
      background-color: #e9ecef;
      border-color: #e9ecef;
      color: #0d6efd;
    }
  }
}

// Dropdown styling
.dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e1e3ea;

  .dropdown-item {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;

    &:hover {
      background-color: #f8f9fa;
    }

    &.text-danger:hover {
      background-color: rgba(220, 53, 69, 0.1);
      color: #dc3545;
    }

    i {
      width: 16px;
      text-align: center;
    }
  }
}

// Form controls
.form-control {
  border-radius: 8px;
  border: 1px solid #e1e3ea;

  &:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }

  &.ps-10 {
    padding-left: 2.5rem;
  }
}

.form-select {
  border-radius: 8px;
  border: 1px solid #e1e3ea;

  &:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }
}

// Utility classes
.fs-8 {
  font-size: 0.75rem !important;
}

.text-success {
  color: #198754 !important;
}
