<div class="row mb-8">
  <div class="col-12">
    <div class="card">
      <div class="card-body p-9">
        <div class="d-flex align-items-center justify-content-between">
          <div class="d-flex align-items-center" [class.flex-row-reverse]="translationService.isRTL()"
            [class.rtl-header-row]="translationService.isRTL()">
            <div
              [class]="translationService.isRTL() ? 'symbol symbol-circle symbol-60px' : 'symbol symbol-circle symbol-60px me-6'">
              <div class="symbol-label bg-light-dark-blue">
                <i class="fas fa-users-cog fs-2x text-dark-blue"></i>
              </div>
            </div>
            <div [style.text-align]="translationService.isRTL() ? 'right' : 'left'"
              [style.font-family]="translationService.isRTL() ? 'Noto <PERSON> Arabic, sans-serif' : 'inherit'">
              <h3 class="fs-2 fw-bold text-gray-900 mb-2"
                [style.font-size]="translationService.isRTL() ? '1.8rem' : 'inherit'">
                {{ 'USER_ACCOUNT_TYPES.TITLE' | translate }}
              </h3>
              <p class="fs-6 fw-semibold text-gray-600 mb-0"
                [style.font-size]="translationService.isRTL() ? '1.1rem' : 'inherit'"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                {{ 'USER_ACCOUNT_TYPES.SUBTITLE' | translate }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row justify-content-center g-6 g-xl-9">
  <div class="col-lg-4 col-md-6" *ngFor="let feature of accountFeatures">
    <app-account-management-card [faIcon]="feature.faIcon" [title]="feature.title" [description]="feature.description"
      [routePath]="feature.routePath" (cardClick)="onCardClick($event)">
    </app-account-management-card>
  </div>
</div>