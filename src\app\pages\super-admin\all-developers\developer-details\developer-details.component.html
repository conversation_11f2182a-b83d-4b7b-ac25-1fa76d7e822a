<!-- Developer Details Content -->
<div class="container-fluid" [class.rtl-layout]="translationService.isRTL()">
  <!-- Header -->
  <div class="row mb-5">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="card-title">
            <div class="d-flex align-items-center">
              <i class="fas fa-user-circle text-primary fs-2" [class.me-3]="!translationService.isRTL()"
                [class.ms-3]="translationService.isRTL()"></i>
              <div>
                <h3 class="mb-0 page-title-custom"
                  [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'"
                  [style.color]="translationService.isRTL() ? '#0D47A1' : 'inherit'">
                  {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.TITLE' | translate }}
                </h3>
                <span class="text-muted fs-6"
                  [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                  {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.SUBTITLE' | translate }}
                </span>
              </div>
            </div>
          </div>
          <div class="card-toolbar">
            <button class="btn btn-secondary btn-sm" (click)="goBack()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fas fa-arrow-left" [class.me-2]="!translationService.isRTL()"
                [class.ms-2]="translationService.isRTL()"></i>
              {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.BACK_TO_DEVELOPERS' | translate }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Developer Profile Card -->
  <div class="row">
    <div class="col-xl-4">
      <div class="card mb-5 mb-xl-8">
        <div class="card-body pt-15">
          <!-- Profile Image -->
          <div class="d-flex flex-center flex-column mb-5">
            <div class="symbol symbol-100px symbol-circle mb-7">
              <img *ngIf="developer?.image" [src]="developer?.image" alt="User Image" class="w-40 h-30"
                style="object-fit: cover; border-radius: 10%" />

            </div>

            <!-- Name and Status -->
            <a class="fs-3 text-gray-800 text-hover-primary fw-bold mb-5">
              {{ developer?.fullName }}
            </a>

            <!-- <div class="fs-5 fw-semibold text-muted mb-6">
              {{ developer.specialization || 'Developer' }}
            </div> -->

            <!-- Status Badge -->
            <div class="d-flex flex-wrap fw-semibold fs-6 mb-4 pe-2">
              <span class="badge fs-7 fw-bold px-3 py-2 status-badge" [ngClass]="getStatusClass(developer?.isActive)"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                {{ (getStatusText(developer?.isActive)) | translate }}
              </span>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="d-flex flex-stack fs-4 py-3">
            <div class="fw-bold contact-info-title"
              [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'"
              [style.color]="translationService.isRTL() ? '#0D47A1' : 'inherit'">
              {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.CONTACT_INFORMATION' | translate }}
            </div>
          </div>

          <div class="separator separator-dashed my-3"></div>

          <div id="kt_customer_view_details" class="collapse show">
            <div class="py-5 fs-6 contact-details">
              <!-- Email -->
              <div class="fw-bold mt-5 contact-label"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.EMAIL' | translate }}
              </div>
              <div class="text-gray-600 contact-value"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                <a [href]="'mailto:' + developer?.email" class="text-gray-600 text-hover-primary">
                  {{ developer?.email }}
                </a>
              </div>

              <!-- Phone -->
              <div class="fw-bold mt-5 contact-label"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.PHONE' | translate }}
              </div>
              <div class="text-gray-600 contact-value"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                <a [href]="'tel:' + developer?.phone" class="text-gray-600 text-hover-primary">
                  {{ developer?.phone }}
                </a>
              </div>

              <!-- Last Login -->
              <div class="fw-bold mt-5 contact-label" *ngIf="developer?.lastLogin"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.LAST_LOGIN' | translate }}
              </div>
              <div class="text-gray-600 contact-value" *ngIf="developer?.lastLogin"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                {{ developer?.lastLogin | date:'medium' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Projects and Statistics -->
    <div class="col-xl-8">
      <!-- Projects Overview -->
      <div class="card mb-5 mb-xl-8">
        <div class="card-header border-0 pt-5">
          <h3 class="card-title align-items-start flex-column">
            <span class="card-label fw-bold fs-3 mb-1 projects-title"
              [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'"
              [style.color]="translationService.isRTL() ? '#0D47A1' : 'inherit'">
              {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.PROJECTS_OVERVIEW' | translate }}
            </span>
            <span class="text-muted mt-1 fw-semibold fs-7"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.DEVELOPER_STATISTICS' | translate }}
            </span>
          </h3>
        </div>

        <div class="card-body py-3">
          <div class="row g-6 g-xl-9">
            <!-- Total Projects -->
            <div class="col-sm-6 col-xl-4">
              <div class="card h-100">
                <div class="card-body d-flex flex-center flex-column py-9 px-5">
                  <div class="symbol symbol-50px symbol-circle mb-5">
                    <span class="symbol-label bg-light-primary">
                      <i class="fas fa-building text-primary fs-2x"></i>
                    </span>
                  </div>
                  <div class="fs-4 fw-bold text-gray-800 mb-2">{{ developer?.numberOfProjects || 0 }}</div>
                  <div class="fw-semibold text-gray-400 stat-label"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.TOTAL_PROJECTS' | translate }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Active Projects -->
            <div class="col-sm-6 col-xl-4">
              <div class="card h-100">
                <div class="card-body d-flex flex-center flex-column py-9 px-5">
                  <div class="symbol symbol-50px symbol-circle mb-5">
                    <span class="symbol-label bg-light-success">
                      <i class="fas fa-check-circle text-success fs-2x"></i>
                    </span>
                  </div>
                  <div class="fs-4 fw-bold text-gray-800 mb-2">{{ developer?.activeProjects || 0 }}</div>
                  <div class="fw-semibold text-gray-400 stat-label"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.ACTIVE_PROJECTS' | translate }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Rating -->
            <div class="col-sm-6 col-xl-4">
              <div class="card h-100">
                <div class="card-body d-flex flex-center flex-column py-9 px-5">
                  <div class="symbol symbol-50px symbol-circle mb-5">
                    <span class="symbol-label bg-light-warning">
                      <i class="fas fa-star text-warning fs-2x"></i>
                    </span>
                  </div>
                  <div class="fs-4 fw-bold text-gray-800 mb-2">{{ developer?.rating || 'N/A' }}</div>
                  <div class="fw-semibold text-gray-400 stat-label"
                    [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                    {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.RATING' | translate }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="card">
        <div class="card-header border-0 pt-5">
          <h3 class="card-title align-items-start flex-column">
            <span class="card-label fw-bold fs-3 mb-1 quick-actions-title"
              [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'"
              [style.color]="translationService.isRTL() ? '#0D47A1' : 'inherit'">
              {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.QUICK_ACTIONS' | translate }}
            </span>
            <span class="text-muted mt-1 fw-semibold fs-7"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.MANAGE_ACCOUNT' | translate }}
            </span>
          </h3>
        </div>

        <div class="card-body py-3">
          <div class="d-flex flex-wrap gap-3" [class.flex-row-reverse]="translationService.isRTL()">
            <a [routerLink]="['/developer/projects']" [queryParams]="{ developerId: developer?.developerId }"
              class="btn btn-primary btn-sm action-btn"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fas fa-building" [class.me-2]="!translationService.isRTL()"
                [class.ms-2]="translationService.isRTL()"></i>
              {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.VIEW_PROJECTS' | translate }}
            </a>

            <button class="btn btn-success btn-sm action-btn" *ngIf="!developer?.isActive"
              (click)="toggleDeveloperStatus()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fas fa-check" [class.me-2]="!translationService.isRTL()"
                [class.ms-2]="translationService.isRTL()"></i>
              {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.ACTIVATE_ACCOUNT' | translate }}
            </button>

            <button class="btn btn-warning btn-sm action-btn" *ngIf="developer?.isActive"
              (click)="toggleDeveloperStatus()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fas fa-pause" [class.me-2]="!translationService.isRTL()"
                [class.ms-2]="translationService.isRTL()"></i>
              {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.SUSPEND_ACCOUNT' | translate }}
            </button>

            <a class="btn btn-info btn-sm action-btn" [routerLink]="['/chat']"
              [queryParams]="{ chatWithUID: developer?.id }"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fas fa-envelope" [class.me-2]="!translationService.isRTL()"
                [class.ms-2]="translationService.isRTL()"></i>
              {{ 'SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.SEND_MESSAGE' | translate }}
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>