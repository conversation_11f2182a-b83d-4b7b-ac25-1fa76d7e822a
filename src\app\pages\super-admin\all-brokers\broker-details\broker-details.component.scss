// Broker Details component styles
.card {
  box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
  border: 0;
  border-radius: 1rem;
}

.symbol {
  .symbol-label {
    font-size: 1rem;
    font-weight: 600;
  }
}

.badge {
  &.badge-light-success {
    background-color: rgba(34, 197, 94, 0.1);
    color: #22c55e;
  }

  &.badge-light-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }

  &.badge-light-primary {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }

  &.badge-light-warning {
    background-color: rgba(251, 191, 36, 0.1);
    color: #fbbf24;
  }

  &.badge-light-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }
}

.btn {
  &.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
  }
}

.text-hover-primary:hover {
  color: #3b82f6 !important;
}

.separator {
  &.separator-dashed {
    border-bottom: 1px dashed #e1e5e9;
  }
}

.rotate {
  transition: transform 0.3s ease;

  &.collapsed {
    transform: rotate(-90deg);
  }
}

.collapsible {
  cursor: pointer;

  &:hover {
    color: #3b82f6;
  }
}

// Enhanced Arabic RTL support for Broker Details
.rtl-layout {
  direction: rtl !important;
  text-align: right !important;

  .card-header {
    .card-title {
      .d-flex.align-items-center {
        flex-direction: row-reverse !important;

        i {
          margin-right: 0 !important;
          margin-left: 1rem !important;
        }

        div {
          text-align: right !important;

          h3 {
            font-family: 'Noto Kufi Arabic', sans-serif !important;
            font-size: 1.8rem !important;
            font-weight: 800 !important;
            color: #1e3a8a !important;
          }

          span {
            font-family: 'Hacen Liner Screen', sans-serif !important;
            font-size: 0.95rem !important;
          }
        }
      }
    }

    .card-toolbar {
      .btn {
        font-family: 'Hacen Liner Screen', sans-serif !important;
        font-weight: 600 !important;
        border-radius: 8px !important;

        i {
          margin-right: 0 !important;
          margin-left: 0.5rem !important;
        }
      }
    }
  }

  .card-body {
    text-align: right !important;

    .d-flex.flex-center.flex-column {
      a {
        font-family: 'Noto Kufi Arabic', sans-serif !important;
        font-size: 1.5rem !important;
        font-weight: 700 !important;
        color: #1f2937 !important;
        text-align: center !important;
      }

      .badge {
        font-family: 'Hacen Liner Screen', sans-serif !important;
        font-size: 0.9rem !important;
        font-weight: 600 !important;
        padding: 0.5rem 1rem !important;
        border-radius: 8px !important;
      }
    }

    .d-flex.flex-stack {
      flex-direction: row-reverse !important;

      .d-flex.align-items-center {
        flex-direction: row-reverse !important;

        i {
          margin-right: 0 !important;
          margin-left: 0.75rem !important;
        }

        .fw-semibold {
          font-family: 'Noto Kufi Arabic', sans-serif !important;
          font-weight: 600 !important;
        }
      }

      .fw-bold {
        font-family: 'Hacen Liner Screen', sans-serif !important;
        font-weight: 700 !important;
      }
    }
  }
}

:host-context(html[lang="ar"]) {
  .container-fluid {
    direction: rtl !important;
    text-align: right !important;
  }

  .card {
    border-radius: 12px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
    border: 1px solid #e5e7eb !important;
    transition: all 0.3s ease !important;

    &:hover {
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12) !important;
      transform: translateY(-2px) !important;
    }
  }

  .card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    border-bottom: 2px solid #e5e7eb !important;

    .card-title h3 {
      font-family: 'Noto Kufi Arabic', sans-serif !important;
      font-size: 1.8rem !important;
      font-weight: 800 !important;
      color: #1e3a8a !important;
    }

    .card-toolbar .btn {
      font-family: 'Hacen Liner Screen', sans-serif !important;
      font-weight: 600 !important;
      border-radius: 8px !important;
      transition: all 0.3s ease !important;

      &:hover {
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
      }
    }
  }

  .symbol img {
    border-radius: 12px !important;
    border: 3px solid #e5e7eb !important;
    object-fit: cover !important;
  }

  .badge {
    font-family: 'Hacen Liner Screen', sans-serif !important;
    border-radius: 8px !important;
    padding: 0.5rem 1rem !important;
  }

  .fw-semibold, .fw-bold {
    font-family: 'Hacen Liner Screen', sans-serif !important;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Noto Kufi Arabic', sans-serif !important;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }

  .symbol-100px {
    width: 80px !important;
    height: 80px !important;
  }

  .fs-3 {
    font-size: 1.5rem !important;
  }
}
