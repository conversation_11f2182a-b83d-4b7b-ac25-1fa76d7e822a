// Broker Details component styles
.card {
  box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
  border: 0;
  border-radius: 1rem;
}

.symbol {
  .symbol-label {
    font-size: 1rem;
    font-weight: 600;
  }
}

.badge {
  &.badge-light-success {
    background-color: rgba(34, 197, 94, 0.1);
    color: #22c55e;
  }

  &.badge-light-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }

  &.badge-light-primary {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }

  &.badge-light-warning {
    background-color: rgba(251, 191, 36, 0.1);
    color: #fbbf24;
  }

  &.badge-light-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }
}

.btn {
  &.btn-sm {
    padding: 6px 12px;
    font-size: 0.875rem;
  }
}

.text-hover-primary:hover {
  color: #3b82f6 !important;
}

.separator {
  &.separator-dashed {
    border-bottom: 1px dashed #e1e5e9;
  }
}

.rotate {
  transition: transform 0.3s ease;

  &.collapsed {
    transform: rotate(-90deg);
  }
}

.collapsible {
  cursor: pointer;

  &:hover {
    color: #3b82f6;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }

  .symbol-100px {
    width: 80px !important;
    height: 80px !important;
  }

  .fs-3 {
    font-size: 1.5rem !important;
  }
}

// Enhanced Header for Arabic
.arabic-header {
  padding: 2rem 2.5rem !important;
  background: #f8fafc !important;
  border-bottom: 1px solid #e2e8f0 !important;

  .d-flex.align-items-center.flex-row-reverse {
    flex-direction: row !important; // Override flex-row-reverse to keep icon first
  }

  .header-icon {
    color: #1565C0 !important;
    font-size: 3rem !important;
    margin-left: 15px !important;
    margin-right: 1.5rem !important; // Space after icon
    order: 1 !important; // Icon comes first
  }

  .header-title {
    font-size: 2rem !important;
    font-weight: 700 !important;
    color: #1565C0 !important;
    margin-bottom: 0.5rem !important;
  }

  .header-subtitle {
    font-size: 1.1rem !important;
    color: #64748b !important;
  }

  // Text container comes after icon
  div[class*="text-end"] {
    order: 2 !important; // Text comes second
    text-align: right !important;
  }

  .back-button-container {
    .btn-outline-primary {
      padding: 0.75rem 2rem !important;
      font-size: 1rem !important;
      font-weight: 600 !important;
      border: 2px solid #1565C0 !important;
      color: #1565C0 !important;
      border-radius: 8px !important;
      transition: all 0.3s ease !important;

      &:hover {
        background: #1565C0 !important;
        color: white !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(21, 101, 192, 0.3) !important;
      }

      i {
        font-size: 0.9rem !important;
      }
    }
  }
}

// RTL Support for Arabic
.rtl-layout {
  direction: rtl !important;
  text-align: right !important;

  // Symbol styling for Arabic only
  .symbol {
    .symbol-label {
      font-size: 1rem !important;
      font-weight: 600 !important;
      margin-right: 58px !important;
    }
  }
}
