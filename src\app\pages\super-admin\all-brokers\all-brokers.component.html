<!-- Main Card with Projects Design -->
<div class="card mb-5 mb-xl-10" [class.rtl-layout]="translationService.isRTL()">
  <div class="card-body pt-3 pb-0">
    <div class="d-flex flex-wrap flex-sm-nowrap">
      <div class="flex-grow-1">
        <div class="d-flex justify-content-between align-items-start flex-wrap"
          [class.flex-row-reverse]="translationService.isRTL()">
          <div class="d-flex my-4">
            <h1 class="fs-2 fw-bolder mt-3 page-title-custom" [class.me-1]="!translationService.isRTL()"
              [class.ms-1]="translationService.isRTL()" [class.text-dark-blue]="!translationService.isRTL()"
              [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'"
              [style.color]="translationService.isRTL() ? '#0D47A1' : 'inherit'">
              {{ 'SUPER_ADMIN.ALL_BROKERS.TITLE' | translate }}
            </h1>
          </div>
          <div class="d-flex my-4">
            <form data-kt-search-element="form" class="w-300px position-relative mb-3" autocomplete="off">
              <i class="fas fa-search fs-2 fs-lg-1 text-gray-500 position-absolute top-50 translate-middle-y"
                [class.ms-3]="!translationService.isRTL()" [class.me-3]="translationService.isRTL()"
                [class.d-none]="translationService.isRTL()"></i>
              <input type="text" name="searchText" [(ngModel)]="searchText" (ngModelChange)="onSearchChange($event)"
                class="form-control form-control-flush bg-light border rounded-pill"
                [class.ps-10]="!translationService.isRTL()" [class.pe-3]="translationService.isRTL()"
                [placeholder]="'SUPER_ADMIN.ALL_BROKERS.SEARCH_PLACEHOLDER' | translate"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
                [style.text-align]="translationService.isRTL() ? 'right' : 'left'" data-kt-search-element="input" />
            </form>
          </div>
          <div class="d-flex my-4" [class.flex-row-reverse]="translationService.isRTL()">
            <div class="position-relative" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <a class="btn btn-sm btn-light-dark-blue cursor-pointer" [class.me-3]="!translationService.isRTL()"
                [class.ms-3]="translationService.isRTL()"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                <i class="fa-solid fa-filter" [class.me-2]="!translationService.isRTL()"
                  [class.ms-2]="translationService.isRTL()"></i>
                {{ 'SUPER_ADMIN.ALL_BROKERS.FILTER' | translate }}
              </a>
            </div>

            <a class="btn btn-sm btn-dark-blue cursor-pointer"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              <i class="fa-solid fa-plus" [class.me-2]="!translationService.isRTL()"
                [class.ms-2]="translationService.isRTL()"></i>
              {{ 'SUPER_ADMIN.ALL_BROKERS.ADD_BROKER' | translate }}
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="table-responsive mb-5" [class.rtl-table]="translationService.isRTL()"
      [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
      <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3 mt-5"
        [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
        <thead>
          <tr class="fw-bold bg-light-dark-blue text-dark-blue me-1 ms-1">
            <th class="w-25px rounded-start" [class.ps-4]="!translationService.isRTL()"
              [class.pe-4]="translationService.isRTL()" [class.rounded-start]="!translationService.isRTL()"
              [class.rounded-end]="translationService.isRTL()">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input" type="checkbox" value="1" data-kt-check="true"
                  data-kt-check-target=".widget-13-check" />
              </div>
            </th>
            <th class="min-w-150px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.ALL_BROKERS.TABLE.BROKER' | translate }}
            </th>
            <th class="min-w-140px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.ALL_BROKERS.TABLE.EMAIL' | translate }}
            </th>
            <th class="min-w-120px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.ALL_BROKERS.TABLE.PHONE' | translate }}
            </th>
            <th class="min-w-100px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.ALL_BROKERS.TABLE.RATING' | translate }}
            </th>
            <th class="min-w-180px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.ALL_BROKERS.TABLE.ADVERTISEMENT_COUNT' | translate }}
            </th>
            <th class="min-w-100px"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.ALL_BROKERS.TABLE.ACTIVE' | translate }}
            </th>
            <th class="min-w-100px rounded-end" [class.text-end]="!translationService.isRTL()"
              [class.text-start]="translationService.isRTL()" [class.pe-4]="!translationService.isRTL()"
              [class.ps-4]="translationService.isRTL()" [class.rounded-end]="!translationService.isRTL()"
              [class.rounded-start]="translationService.isRTL()"
              [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
              {{ 'SUPER_ADMIN.ALL_BROKERS.TABLE.ACTIONS' | translate }}
            </th>
          </tr>
        </thead>

        <tbody>
          <tr *ngFor="let broker of brokers">
            <!-- Checkbox Column -->
            <td [class.ps-4]="!translationService.isRTL()" [class.pe-4]="translationService.isRTL()">
              <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input widget-13-check" type="checkbox" value="1" />
              </div>
            </td>

            <!-- Broker Column (Name + Image) -->
            <td>
              <div class="d-flex align-items-center">
                <div class="symbol symbol-40px symbol-circle me-3">
                  <img *ngIf="broker.image" [src]="broker.image" alt="" />
                  <div *ngIf="!broker.image" class="symbol-label bg-light-primary text-primary fw-bold fs-6">
                    {{ broker.fullName?.charAt(0) }}
                  </div>
                </div>
                <div class="d-flex justify-content-start flex-column">
                  <a [routerLink]="['/super-admin/all-brokers/broker-details']" [queryParams]="{ brokerId: broker.id }"
                    class="text-gray-800 fw-bold text-hover-primary fs-6">
                    {{ broker.fullName }}
                  </a>
                  <!-- <span class="text-muted fw-semibold fs-7 mb-1">{{ broker.email }}</span>
                  <span class="text-muted fw-semibold fs-7 d-lg-none">{{ broker.phone }}</span> -->
                </div>
              </div>
            </td>

            <!-- Email Column -->
            <td class="d-none d-lg-table-cell">
              <span class="text-gray-800 fw-semibold fs-6">{{ broker.email }}</span>
            </td>

            <!-- Phone Column -->
            <td class="d-none d-lg-table-cell">
              <span class="text-gray-800 fw-semibold fs-6">{{ broker.phone }}</span>
            </td>

            <!-- Rating Column -->
            <td class="d-none d-xl-table-cell">
              <div class="d-flex align-items-center">
                <i class="fas fa-star text-warning fs-6 me-2"></i>
                <span class="text-gray-800 fw-bold fs-6">{{ broker.avgRating }}</span>
              </div>
            </td>

            <!-- Advertisement Count Column -->
            <td class="d-none d-xl-table-cell">
              <span class="badge badge-light-info fs-7 fw-bold">{{ broker.advertisementCount }}</span>
            </td>

            <!-- Active Column -->
            <td>
              <span class="badge fs-7 fw-bold px-3 py-2" [ngClass]="getStatusClass(broker.isActive)">
                {{ broker.isActive ? ('SUPER_ADMIN.COMMON.ACTIVE' | translate) : ('SUPER_ADMIN.COMMON.INACTIVE' |
                translate) }}
              </span>
            </td>

            <!-- Actions Column -->
            <td [class.text-end]="!translationService.isRTL()" [class.text-start]="translationService.isRTL()"
              [class.pe-4]="!translationService.isRTL()" [class.ps-4]="translationService.isRTL()">
              <button class="btn btn-sm btn-icon btn-light-primary" data-bs-toggle="tooltip"
                [title]="'SUPER_ADMIN.ALL_BROKERS.VIEW_DETAILS' | translate" (click)="viewBroker(broker)">
                <i class="fas fa-eye fs-6"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div *ngIf="!loading && brokers.length > 0" class="d-flex justify-content-center mt-5 mb-5">
      <app-pagination [totalItems]="totalElements" [itemsPerPage]="pageSize" [currentPage]="currentPage"
        (pageChange)="onPageChange($event)"></app-pagination>
    </div>
  </div>
</div>