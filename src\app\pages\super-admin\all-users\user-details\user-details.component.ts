import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UsersService } from '../../services/users.service';
import { TranslationService } from '../../../../modules/i18n';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-user-details',
  templateUrl: './user-details.component.html',
  styleUrls: ['./user-details.component.scss']
})
export class UserDetailsComponent implements OnInit {
  user: any = null;

  userId: number;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private usersService: UsersService,
    private cd: ChangeDetectorRef,
    public translationService: TranslationService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.userId = params['userId'];
      if (this.userId) {
        this.loadUserDetails();
      } else {
        this.router.navigate(['/super-admin/all-users']);
      }
    });
  }

  loadUserDetails(): void {
    this.usersService.getUserById(this.userId).subscribe({
      next: (response) => {
        console.log('User details:', response);
        this.user = response.data ;
        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading user details:', error);
        Swal.fire('Error', 'Failed to load user details. Please try again.', 'error');
        this.router.navigate(['/super-admin/all-users']);
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/super-admin/all-users']);
  }

  getInitials(name: string): string {
    if (!name) return '';
    return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase();
  }



  toggleUserStatus(): void {
    if (!this.user) return;

    const action = this.user.isActive ? 'suspend' : 'activate';
    const message = this.user.isActive ? 'suspend this user account?' : 'activate this user account?';

    Swal.fire({
      title: 'Are you sure?',
      text: `Do you want to ${message}`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: `Yes, ${action}!`
    }).then((result) => {
      if (result.isConfirmed) {
        this.usersService.toggleUserStatus(this.user.id).subscribe({
          next: (response) => {
            console.log('Status toggled successfully:', response);
            this.user.isActive = !this.user.isActive;
            Swal.fire(
              'Success!',
              `User account has been ${this.user.isActive ? 'activated' : 'suspended'}.`,
              'success'
            ).then(() => {
              // Refresh the page after success message
              window.location.reload();
            });
          },
          error: (error) => {
            console.error('Error toggling status:', error);
            Swal.fire('Error', 'Failed to update user status. Please try again.', 'error');
          }
        });
      }
    });
  }

  getStatusText(status: boolean): string {
    return status ? 'SUPER_ADMIN.COMMON.ACTIVE' : 'SUPER_ADMIN.COMMON.INACTIVE';
  }

  getStatusClass(status: boolean): string {
    return status ? 'badge-light-success' : 'badge-light-danger';
  }

  getVerificationText(verified: boolean): string {
    return verified ? 'SUPER_ADMIN.ALL_USERS.VERIFIED' : 'SUPER_ADMIN.ALL_USERS.NOT_VERIFIED';
  }

  getVerificationClass(verified: boolean): string {
    return verified ? 'badge-light-success' : 'badge-light-warning';
  }

  getUserTypeClass(role: string): string {
    switch (role?.toLowerCase()) {
      case 'admin':
        return 'badge-light-danger';
      case 'developer':
        return 'badge-light-primary';
      case 'broker':
        return 'badge-light-info';
      case 'client':
        return 'badge-light-success';
      default:
        return 'badge-light-secondary';
    }
  }

  getUserRoleText(role: string): string {
    switch (role?.toLowerCase()) {
      case 'admin':
        return 'مدير';
      case 'developer':
        return 'مطور';
      case 'broker':
        return 'وسيط';
      case 'client':
        return 'عميل';
      default:
        return role || 'N/A';
    }
  }
}
