import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DevelopersService } from '../../services/developers.service';
import Swal from 'sweetalert2';
import { TranslationService } from 'src/app/modules/i18n';

@Component({
  selector: 'app-developer-details',
  templateUrl: './developer-details.component.html',
  styleUrls: ['./developer-details.component.scss']
})
export class DeveloperDetailsComponent implements OnInit {
  developer: any = null;

  developerId: number;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private developersService: DevelopersService,
    private cd: ChangeDetectorRef,
    public translationService: TranslationService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.developerId = params['developerId'];
      if (this.developerId) {
        this.loadDeveloperDetails();
      } else {
        this.router.navigate(['/super-admin/all-developers']);
      }
    });
  }

  loadDeveloperDetails(): void {


    this.developersService.getDeveloperById(this.developerId).subscribe({
      next: (response) => {
        console.log('Developer details:', response);
        this.developer = response.data ;
        this.cd.detectChanges();

      },
      error: (error) => {
        console.error('Error loading developer details:', error);

        Swal.fire(
          this.translationService.translate('SUPER_ADMIN.COMMON.ERROR'),
          this.translationService.translate('SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.ERROR_LOADING'),
          'error'
        );
        this.router.navigate(['/super-admin/all-developers']);
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/super-admin/all-developers']);
  }

  getInitials(name: string): string {
    if (!name) return '';
    return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase();
  }

  getStatusClass(status: boolean): string {
    return status ? 'badge-light-success' : 'badge-light-danger';
  }

  getStatusText(status: boolean): string {
    if (status) {
      return this.translationService.translate('SUPER_ADMIN.COMMON.ACTIVE');
    } else {
      return this.translationService.translate('SUPER_ADMIN.COMMON.INACTIVE');
    }
  }

  toggleDeveloperStatus(): void {
    if (!this.developer) return;

    const isActivating = !this.developer.isActive;
    const confirmMessage = isActivating
      ? this.translationService.translate('SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.CONFIRM_ACTIVATE')
      : this.translationService.translate('SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.CONFIRM_SUSPEND');

    const confirmButtonText = isActivating
      ? this.translationService.translate('SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.ACTIVATE_ACCOUNT')
      : this.translationService.translate('SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.SUSPEND_ACCOUNT');

    Swal.fire({
      title: this.translationService.translate('SUPER_ADMIN.COMMON.ARE_YOU_SURE'),
      text: confirmMessage,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: confirmButtonText,
      cancelButtonText: this.translationService.translate('SUPER_ADMIN.COMMON.CANCEL'),
      customClass: {
        popup: this.translationService.isRTL() ? 'custom-swal-arabic' : 'custom-swal-english'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        this.developersService.toggleDeveloperStatus(this.developer.id).subscribe({
          next: (response) => {
            console.log('Status toggled successfully:', response);
            this.developer.isActive = !this.developer.isActive;

            const successMessage = this.developer.isActive
              ? this.translationService.translate('SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.ACCOUNT_ACTIVATED')
              : this.translationService.translate('SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.ACCOUNT_SUSPENDED');

            Swal.fire({
              title: this.translationService.translate('SUPER_ADMIN.COMMON.SUCCESS'),
              text: successMessage,
              icon: 'success',
              customClass: {
                popup: this.translationService.isRTL() ? 'custom-swal-arabic' : 'custom-swal-english'
              }
            }).then(() => {
              // Refresh the page after success message
              window.location.reload();
            });
          },
          error: (error) => {
            console.error('Error toggling status:', error);
            Swal.fire({
              title: this.translationService.translate('SUPER_ADMIN.COMMON.ERROR'),
              text: this.translationService.translate('SUPER_ADMIN.ALL_DEVELOPERS.DEVELOPER_DETAILS.ERROR_STATUS_UPDATE'),
              icon: 'error',
              customClass: {
                popup: this.translationService.isRTL() ? 'custom-swal-arabic' : 'custom-swal-english'
              }
            });
          }
        });
      }
    });
  }
}
