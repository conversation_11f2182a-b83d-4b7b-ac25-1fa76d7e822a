// RTL Support for Projects Page
.rtl-layout {
  direction: rtl;
  text-align: right;

  h1 {
    font-family: 'Hacen Liner Screen St', sans-serif;
    font-weight: bold;
  }

  .btn {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .form-control {
    text-align: right;
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .table {
    th, td {
      text-align: right;
      font-family: 'Hacen Liner Screen St', sans-serif;
    }

    th {
      font-size: 1.1rem;
      font-weight: bold;
    }
  }

  .badge {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .text-muted {
    font-family: 'Hacen Liner Screen St', sans-serif;
  }

  .card-body {
    h4, h5, p {
      font-family: 'Hacen Liner Screen St', sans-serif;
    }
  }

  // Reverse margins and paddings
  .me-1, .me-3 {
    margin-left: 0.25rem !important;
    margin-right: 0 !important;
  }

  .ms-3 {
    margin-right: 0.75rem !important;
    margin-left: 0 !important;
  }

  .ps-10 {
    padding-right: 2.5rem !important;
    padding-left: 0 !important;
  }

  // Search input positioning
  .position-absolute.top-50.translate-middle-y.ms-3 {
    right: 0.75rem !important;
    left: auto !important;
  }

  // Filter dropdown positioning
  .dropdown-menu {
    right: auto !important;
    left: 0 !important;
  }
}

// RTL Header specific styles
.rtl-header {
  direction: rtl;

  .d-flex {
    gap: 1rem !important;
  }

  .mx-3 {
    margin-left: 1rem !important;
    margin-right: 1rem !important;
  }

  .gap-3 {
    gap: 1rem !important;
  }

  h1 {
    font-family: 'Hacen Liner Screen St', sans-serif;
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
  }

  .btn {
    font-family: 'Hacen Liner Screen St', sans-serif;
    white-space: nowrap;
  }

  .form-control {
    direction: rtl;
    text-align: right;
    font-family: 'Hacen Liner Screen St', sans-serif;
  }
}

// Enhanced RTL support for Arabic
:host-context(html[lang="ar"]) {
  .card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
  }

  .card-body {
    direction: rtl;
    padding: 1.5rem !important;

    // Header improvements
    .d-flex.flex-wrap.flex-sm-nowrap {
      .flex-grow-1 {
        .d-flex.justify-content-between.align-items-start.flex-wrap {
          gap: 1rem !important;
          margin-bottom: 1.5rem !important;

          .d-flex.my-4 {
            margin: 0.5rem 0 !important;

            &:first-child {
              flex: 1;

              h1 {
                font-family: 'Noto Kufi Arabic', sans-serif !important;
                font-size: 1.8rem !important;
                font-weight: 800 !important;
                color: #1e3a8a !important;
                margin: 0 !important;
                text-align: right !important;
              }
            }

            &:nth-child(2) {
              .position-relative {
                .form-control {
                  font-family: 'Hacen Liner Screen', sans-serif !important;
                  text-align: right !important;
                  direction: rtl !important;
                  border-radius: 25px !important;
                  padding: 0.75rem 3rem 0.75rem 1rem !important;
                  border: 2px solid #e5e7eb !important;
                  background: #f8fafc !important;

                  &:focus {
                    border-color: #1e3a8a !important;
                    box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25) !important;
                    background: white !important;
                  }

                  &::placeholder {
                    color: #9ca3af !important;
                    font-family: 'Hacen Liner Screen', sans-serif !important;
                  }
                }

                app-keenicon {
                  right: 1rem !important;
                  left: auto !important;
                  color: #6b7280 !important;
                }
              }
            }

            &:last-child {
              gap: 0.75rem !important;

              .btn {
                font-family: 'Hacen Liner Screen', sans-serif !important;
                font-weight: 600 !important;
                border-radius: 8px !important;
                padding: 0.6rem 1.2rem !important;
                white-space: nowrap !important;
                transition: all 0.3s ease !important;

                &.btn-light-dark-blue {
                  background: rgba(30, 58, 138, 0.1) !important;
                  color: #1e3a8a !important;
                  border: 1px solid rgba(30, 58, 138, 0.2) !important;

                  &:hover {
                    background: rgba(30, 58, 138, 0.2) !important;
                    transform: translateY(-1px) !important;
                    box-shadow: 0 2px 8px rgba(30, 58, 138, 0.2) !important;
                  }
                }

                &.btn-dark-blue {
                  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
                  border: none !important;
                  color: white !important;

                  &:hover {
                    background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%) !important;
                    transform: translateY(-1px) !important;
                    box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3) !important;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // Table improvements
  .table-responsive {
    margin-top: 1rem !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05) !important;


    .table {
      margin-bottom: 0 !important;

      thead {
        tr {
          background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%) !important;

          th {
            text-align: center !important;
            font-family: 'Noto Kufi Arabic', sans-serif !important;
            font-size: 1rem !important;
            font-weight: 700 !important;
            color: #1e3a8a !important;
            padding: 1.2rem 0.75rem !important;
            border-bottom: 2px solid #cbd5e1 !important;
            vertical-align: middle !important;
            white-space: nowrap !important;

            &:first-child {
              text-align: right !important;
              padding-right: 1.5rem !important;
              border-top-right-radius: 12px !important;
              width: 50px !important;
            }

            &:last-child {
              text-align: left !important;
              padding-left: 1.5rem !important;
              border-top-left-radius: 12px !important;
              width: 80px !important;
            }
          }
        }
      }

      tbody {
        tr {
          border-bottom: 1px solid #f1f5f9 !important;
          transition: all 0.2s ease !important;

          &:hover {
            background: rgba(30, 58, 138, 0.03) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
          }

          &:last-child {
            border-bottom: none !important;
          }

          td {
            text-align: center !important;
            font-family: 'Hacen Liner Screen', sans-serif !important;
            padding: 1.2rem 0.75rem !important;
            vertical-align: middle !important;
            border-bottom: none !important;

            &:first-child {
              text-align: right !important;
              padding-right: 1.5rem !important;
            }

            &:last-child {
              text-align: left !important;
              padding-left: 1.5rem !important;
            }

            // Project name and details
            .d-flex.align-items-center {
              justify-content: flex-start !important;
              gap: 1rem !important;

              .symbol {
                flex-shrink: 0 !important;

                img {
                  border-radius: 8px !important;
                  border: 2px solid #e5e7eb !important;
                  object-fit: cover !important;
                }
              }

              .d-flex.justify-content-start.flex-column {
                text-align: right !important;
                flex: 1 !important;

                a {
                  font-family: 'Noto Kufi Arabic', sans-serif !important;
                  font-size: 1rem !important;
                  font-weight: 600 !important;
                  color: #1f2937 !important;
                  text-decoration: none !important;
                  margin-bottom: 0.25rem !important;

                  &:hover {
                    color: #1e3a8a !important;
                  }
                }

                .text-muted {
                  font-family: 'Hacen Liner Screen', sans-serif !important;
                  font-size: 0.85rem !important;
                  color: #6b7280 !important;
                }
              }
            }

            // Numbers styling
            .text-gray-800 {
              font-family: 'Noto Kufi Arabic', sans-serif !important;
              font-size: 1.1rem !important;
              font-weight: 700 !important;
              color: #1f2937 !important;
            }

            // Badge styling
            .badge {
              font-family: 'Hacen Liner Screen', sans-serif !important;
              font-size: 0.8rem !important;
              font-weight: 600 !important;
              padding: 0.4rem 0.8rem !important;
              border-radius: 6px !important;
            }

            // Action buttons
            .btn-icon {
              border-radius: 6px !important;
              transition: all 0.2s ease !important;

              &:hover {
                background: rgba(30, 58, 138, 0.1) !important;
                transform: scale(1.1) !important;
              }
            }
          }
        }
      }
    }
  }

  // Pagination improvements
  .d-flex.justify-content-center {
    margin-top: 1.5rem !important;
    margin-bottom: 1rem !important;
  }
}
