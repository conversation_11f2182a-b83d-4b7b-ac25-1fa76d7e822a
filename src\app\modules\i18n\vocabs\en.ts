// USA
export const locale = {
  lang: 'en',
  data: {
    TRANSLATOR: {
      SELECT: 'Select your language',
    },
    MENU: {
      NEW: 'new',
      ACTIONS: 'Actions',
      CREATE_POST: 'Create New Post',
      PAGES: 'Pages',
      FEATURES: 'Features',
      APPS: 'Apps',
      DASHBOARD: 'Dashboard',
      REQUESTS: 'Requests',
      DATA_PROPERTIES: 'Data & Properties',
      DEVELOPERS: 'Developers',
      SUBSCRIPTION: 'Subscription',
      PROJECTS: 'Projects',
      BROKERS: 'Brokers',
      USERS: 'Users',
      MESSAGES: 'Messages',
      PROFILE: 'Profile',
      NOTIFICATIONS: 'Notifications',
      HELP: 'Help',
      ADVERTISEMENTS: 'Advertisements',
      MAPS: 'Maps',
      SETTINGS: 'Settings',
      ALL_PROJECTS: 'All Projects',
      ALL_DEVELOPERS: 'All Developers',
      ALL_BROKERS: 'All Brokers',
      ALL_USERS: 'All Users'
    },
    DEVELOPERS: {
      TITLE: 'Developers',
      SEARCH_PLACEHOLDER: 'Search...',
      ALL_DEVELOPERS: 'All Developers',
      CONTRACTED_DEVELOPERS: 'Contracted Developers',
      NOT_CONTRACTED_DEVELOPERS: 'Not Contracted Developers',
      DEVELOPER: 'Developer',
      CONTRACT_DURATION: 'Contract Duration',
      CONTRACT_DATE: 'Contract Date',
      CONTRACT_END_DATE: 'Contract End-Date',
      STATUS: 'Status',
      ACTIONS: 'Actions',
      PROJECTS: 'projects',
      SEND_CONTRACT_REQUEST: 'Send Contract Request',
      CONTRACTED: 'Contracted',
      VIEW_DEVELOPER: 'View Developer',
      SEND_CONTRACT_REQUEST_MODAL: 'Send Contract Request',
      UPLOAD_DOCUMENTS: 'Please Upload The Required Documents',
      UPLOAD_DOCUMENTS_DESC: 'You can upload the required documents now or skip and upload them later. Please note: You will not be able to perform any transaction before completing the required documents.',
      PROFILE_PICTURE: 'Profile picture for account',
      NATIONAL_ID_FRONT: 'National ID photo from the front',
      NATIONAL_ID_BACK: 'National ID card photo from the back',
      FILE_SIZE: 'Size 12 KB · Max 1M',
      CANCEL: 'Cancel',
      SEND_REQUEST: 'Send Request',
      LOADING: 'Loading...'
    },
    DATA_PROPERTIES: {
      TITLE: 'Data and Properties',
      SUBTITLE: 'View and manage your property data',
      SEARCH_PLACEHOLDER: 'Search by unit type...',
      FILTER: 'Filter',
      UPLOAD: 'Upload',
      TEMPLATE: 'Template',
      ADD_UNIT: 'Add Unit',
      ADD: 'Add',
      LOADING: 'Loading...',
      COMPOUND_TYPE: 'Compound Type',
      ALL_COMPOUND_TYPES: 'All Compound Types',
      OUTSIDE_COMPOUND: 'Outside Compound',
      INSIDE_COMPOUND: 'Inside Compound',
      VILLAGE: 'Village',
      UNIT_TYPE: 'Unit Type',
      SELECT_UNIT_TYPE: 'Select Unit Type',
      SELECT_COMPOUND_TYPE_FIRST: 'Select Compound Type First',
      APPLY_FILTER: 'Apply Filter',
      CLEAR_ALL_FILTERS: 'Clear All Filters'
    },
    UNIT_FILTER: {
      AREA: 'Area',
      SELECT: 'Select',
      COMPOUND_TYPE: 'Compound Type',
      SELECT_COMPOUND_TYPE: 'Select Compound Type',
      UNIT_AREA: 'Unit Area',
      ENTER_UNIT_AREA: 'Enter unit area',
      VIEW: 'View',
      UNIT_TYPE: 'Unit Type',
      SELECT_UNIT_TYPE: 'Select Unit Type',
      PRICE: 'Price',
      ENTER_PRICE: 'Enter price',
      FINISHING_STATUS: 'Finishing Status',
      SELECT_FINISHING: 'Select Finishing',
      STATUS: 'Status',
      SELECT_STATUS: 'Select Status',
      APPLY: 'Apply',
      RESET: 'Reset'
    },
    ADD_PROPERTY: {
      // Headers
      ADD_PROPERTY: 'Add Property',
      ADD_UNIT: 'Add Unit',
      PROPERTY_CATEGORY: 'Property Category',
      LOCATION_INFORMATION: 'Location Information',
      UNIT_INFORMATION: 'Unit Information',
      PAYMENT_DETAILS: 'Payment Details',
      MEDIA_DOCUMENTS: 'Media & Documents',
      REVIEW_SUBMIT: 'Review & Submit',

      // Progress
      STEP: 'Step',
      OF: 'of',
      BACK_TO_PREVIOUS_STEP: 'Back to previous step',

      // Step 0 - Property Category
      COMPOUND_TYPE: 'Compound Type',
      SELECT_COMPOUND_TYPE: 'Select Compound Type',
      PROPERTY_TYPE: 'Property Type',
      SELECT_PROPERTY_TYPE: 'Select property type',
      UNIT_TYPE: 'Unit Type',
      SELECT_UNIT_TYPE: 'Select Unit Type',
      SELECT_COMPOUND_TYPE_FIRST: 'Please select compound type first',

      // Step 1 - Location Information
      OWNER_NAME: 'Owner Name',
      ENTER_NAME: 'Enter name',
      OWNER_PHONE: 'Owner Phone',
      ENTER_PHONE_NUMBER: 'Enter phone number',
      PHONE_REQUIRED: 'Phone number is required.',
      PHONE_PATTERN_ERROR: 'Please enter a valid Egyptian phone number (e.g., 01XXXXXXXXX).',
      CITY: 'City',
      SELECT_CITY: 'Select City',
      LOADING_CITIES: 'Loading cities...',
      NO_CITIES_AVAILABLE: 'No cities available',
      LOADING: 'Loading...',
      TOTAL_CITIES: 'Total - Cities',
      AREA: 'Area',
      SELECT_AREA: 'Select Area',
      SUB_AREA: 'Sub Area',
      SELECT_SUB_AREA: 'Select sub area',
      DETAILED_ADDRESS: 'Detailed Address',
      ENTER_DETAILED_ADDRESS: 'Enter detailed address',
      MALL_NAME: 'Mall Name',
      ENTER_MALL_NAME: 'Enter mall name (optional)',
      MALL_NAME_MAX_LENGTH: 'Mall name cannot exceed 255 characters.',
      COMPOUND_NAME: 'Compound Name',
      ENTER_COMPOUND_NAME: 'Enter compound name (optional)',
      COMPOUND_NAME_MAX_LENGTH: 'Compound name cannot exceed 255 characters.',
      GOOGLE_MAPS_LINK: 'Google Maps Link',
      ENTER_GOOGLE_MAPS_LINK: 'Enter Google Maps link',
      GOOGLE_MAPS_REQUIRED: 'Google Maps link is required.',
      GOOGLE_MAPS_PATTERN_ERROR: 'Please enter a valid URL (e.g., https://maps.google.com/...).'
    },
    BROKER: {
      MY_ADVERTISEMENTS: {
        TITLE: 'My Advertisements',
        SUBTITLE: 'Manage and view all your property listings',
        TOTAL: 'Total',
        ADD_NEW: 'Add New',
        CREATE_NEW_ADVERTISEMENT: 'Create New Advertisement',
        NO_ADVERTISEMENTS_FOUND: 'No Advertisements Found',
        NO_ADVERTISEMENTS_MESSAGE: 'You have no advertisements yet. Start by creating your first advertisement to show available properties.',
        VIEW: 'View',
        AREA: 'Area',
        CITY: 'City',
        VIDEO: 'Video',
        IMAGE: 'Image',
        OPEN_IN_NEW_TAB: 'Open in new tab'
      },
      MY_MAPS: {
        TITLE: 'My Maps',
        UPLOAD_MAP: 'Upload Map',
        NO_MAPS_UPLOADED: 'No Maps Uploaded',
        NO_MAPS_MESSAGE: 'Upload your maps to get started.',
        UPLOAD: 'Upload',
        CHOOSE_FILE: 'Choose file',
        DESCRIPTION: 'Description',
        DESCRIPTION_PLACEHOLDER: 'Enter a description for the file',
        CANCEL: 'Cancel',
        IMAGE_GALLERY: 'Image gallery',
        NO_IMAGES_AVAILABLE: 'No images available'
      },
      DASHBOARD: {
        CREATE_NEW_REQUEST: 'Create New Request',
        REQUEST: 'Request',
        REQUESTS: 'Requests',
        DEVELOPERS: 'Developers',
        MY_MAPS: 'My Maps',
        MAPS: 'Maps',
        MY_ADVERTISEMENTS: 'My Advertisements',
        ADS: 'Ads',
        DATA_AND_PROPERTIES: 'Data and Properties',
        PROPERTIES: 'Properties',
        VIEW_ALL: 'View all',
        CREATE: 'Create',
        OVER: 'Over',
        REQUESTS_AND_SALES_STATISTICS: 'Requests and Sales Statistics',
        FINISHED_REQUESTS: 'Finished Requests',
        IN_PROCESSING_REQUESTS: 'In Processing Requests',
        FILTER_REQUESTS: 'Filter Requests',
        FILTER_BY_SPECIALIZATIONS: 'Filter requests by specializations'
      },
      SUBSCRIPTION: {
        GOLDEN_ACCOUNT: 'Golden Account',
        SILVER_ACCOUNT: 'Silver Account',
        BRONZE_ACCOUNT: 'Bronze Account',
        ANNUAL: 'Annual',
        SPECIALIZATIONS: 'Specializations',
        LOCATIONS: 'Locations',
        ADVERTISEMENTS: 'Advertisements',
        OPERATIONS: 'Operations',
        SUBSCRIBE: 'Subscribe',
        EGP: 'EGP'
      }
    },
    BROKER_TITLE: {
      HELLO: 'Hello -',
      CREATE_NEW_REQUEST: 'Create New Request',
      PROFILE_IMAGE_ALT: 'Profile Image',
      USER_ALT: 'User'
    },
    AUTH: {
      GENERAL: {
        OR: 'Or',
        SUBMIT_BUTTON: 'Submit',
        NO_ACCOUNT: 'Don\'t have an account?',
        SIGNUP_BUTTON: 'Sign Up',
        FORGOT_BUTTON: 'Forgot Password',
        BACK_BUTTON: 'Back',
        PRIVACY: 'Privacy',
        LEGAL: 'Legal',
        CONTACT: 'Contact',
        NEED_HELP: 'Need help?',
        CONTACT_US: 'Contact us',
        ALREADY_HAVE_ACCOUNT: 'Already have an account?',
        GO_TO_LOGIN: 'Go to login',
        CREATE_ONE: 'Create one',
        DONT_HAVE_ACCOUNT: 'Don\'t have an account?'
      },
      LOGIN: {
        TITLE: 'Login to your account',
        BUTTON: 'Log In',
        PHONE: 'Phone',
        PASSWORD: 'Password',
        PHONE_PLACEHOLDER: '01xxxxxxxxx',
        PASSWORD_PLACEHOLDER: 'Enter your password',
        FORGOT_PASSWORD: 'Forgot your password?',
        REMEMBER_ME: 'Remember me',
        LOGGING_IN: 'Logging in...',
        LOG_IN: 'Log In'
      },
      FORGOT: {
        TITLE: 'Forgot Password',
        DESC: 'Don\'t worry, we\'ll send you password recovery instructions',
        SUCCESS: 'Your account has been successfully reset.',
        EMAIL_OR_MOBILE: 'Email or Mobile Number',
        EMAIL_OR_MOBILE_PLACEHOLDER: 'Enter your email or mobile number',
        SEND_VERIFICATION_CODE: 'Send Verification Code',
        SENDING: 'Sending...',
        BACK_TO_LOGIN: 'Back to Login'
      },
      VERIFICATION: {
        TITLE: 'Enter Verification Code',
        RESEND_IN: 'Resend in',
        RESEND_CODE: 'Resend Code',
        VERIFYING: 'Verifying...',
        VERIFIED_NEXT: 'Verified - Next',
        BACK_TO_FORGOT_PASSWORD: 'Back to Forgot Password'
      },
      RESET_PASSWORD: {
        TITLE: 'Reset Password',
        DESC: 'Enter your new password for your account',
        NEW_PASSWORD: 'New Password',
        NEW_PASSWORD_PLACEHOLDER: 'Enter your new password',
        CONFIRM_PASSWORD: 'Confirm Password',
        CONFIRM_PASSWORD_PLACEHOLDER: 'Re-enter your password',
        SAVE_NEW_PASSWORD: 'Save New Password',
        SAVING: 'Saving...',
        BACK_TO_VERIFICATION: 'Back to Verification Code'
      },
      REGISTER: {
        TITLE: 'Sign Up',
        DESC: 'Enter your details to create your account',
        SUCCESS: 'Your account has been successfuly registered.',
        CHOOSE_ACCOUNT_TYPE: 'Choose your account type',
        CHOOSE_ACCOUNT_DESC: 'Choose the account type that you want to register with, which matches your work and needs in Easy deal',
        CLIENT: 'Client',
        BROKER: 'Broker',
        DEVELOPER: 'Developer',
        CHOOSE: 'Choose'
      },
      INPUT: {
        EMAIL: 'Email',
        FULLNAME: 'Full Name',
        PASSWORD: 'Password',
        CONFIRM_PASSWORD: 'Confirm Password',
        USERNAME: 'Username',
        PHONE: 'Phone',
        EMAIL_OR_PHONE: 'Enter your email or phone number',
        EMAIL_OR_PHONE_PLACEHOLDER: '<EMAIL> or 01xxxxxxxxx',
        EMAIL_PLACEHOLDER: '<EMAIL>..',
        PHONE_PLACEHOLDER: '01xxxxxxxxx',
        PASSWORD_PLACEHOLDER: '********',
        NAME_PLACEHOLDER: 'Name'
      },
      VALIDATION: {
        INVALID: '{{name}} is not valid',
        REQUIRED: '{{name}} is required',
        MIN_LENGTH: '{{name}} minimum length is {{min}}',
        AGREEMENT_REQUIRED: 'Accepting terms & conditions are required',
        NOT_FOUND: 'The requested {{name}} is not found',
        INVALID_LOGIN: 'The login detail is incorrect',
        REQUIRED_FIELD: 'Required field',
        MIN_LENGTH_FIELD: 'Minimum field length:',
        MAX_LENGTH_FIELD: 'Maximum field length:',
        INVALID_FIELD: 'Field is not valid',
        SELECT_GENDER: 'Please select your gender',
        PASSWORDS_NOT_MATCH: 'Passwords do not match'
      },
      CLIENT_REGISTRATION: {
        TITLE: 'Register as a Client',
        STEP_OF: 'Step {{current}} of {{total}}',
        BACK_TO_PREVIOUS: 'Back to previous step',
        CHOOSE_GENDER: 'Choose Gender',
        FEMALE: 'Female',
        MALE: 'Male',
        FULL_NAME: 'Full Name',
        EMAIL_PHONE_PASSWORD: 'Email, Phone and Password',
        SEND_VERIFICATION_CODE: 'Send Verification Code',
        SENDING: 'Sending...',
        ENTER_VERIFICATION_CODE: 'Enter Verification Code',
        VERIFICATION_CODE_NEXT: 'Verification Code - Next',
        VERIFYING: 'Verifying...',
        CREATE_ACCOUNT: 'Create Account',
        CREATING_ACCOUNT: 'Creating Account...',
        REGISTRATION_SUCCESS: 'registration Success',
        SUCCESS_MESSAGE: 'Your account has been successfully created. You can now enjoy the various and amazing services provided by Easy deal through the website or dashboard.',
        GO_TO_WEBSITE: 'Go to website',
        LEARN_MORE: 'Learn all about your account and how to get started',
        AGREE_TERMS: 'I agree to the Terms and Conditions'
      },
      BROKER_REGISTRATION: {
        TITLE: 'Broker Registration',
        BASIC_INFO: 'Enter Your Basic Information',
        ENTER_FULL_NAME: 'Enter full name...',
        ENTER_EMAIL_PHONE: 'Enter Email or Phone Number',
        EMAIL_PHONE_EXAMPLE: '<EMAIL> or ***********',
        CHOOSE_BROKER_TYPE: 'Choose Broker Type',
        BROKER_TYPE_DESC: 'Please select the type of broker registration you want to proceed with',
        INDEPENDENT_BROKER: 'Independent Broker',
        INDEPENDENT_BROKER_DESC: 'Register as an individual real estate broker',
        REAL_ESTATE_COMPANY: 'Real Estate Company',
        REAL_ESTATE_COMPANY_DESC: 'Register as a real estate brokerage company',
        CONTINUE: 'Continue',
        UPLOAD_DOCUMENTS: 'Please Upload Required Documents',
        DOCUMENTS_DESC: 'You can upload the required documents now or skip and add them later when you first use the required services',
        PROFILE_PHOTO: 'Profile Photo',
        ID_DOCUMENT: 'ID Document',
        NATIONAL_ID_FRONT: 'National ID Front',
        NATIONAL_ID_BACK: 'National ID Back',
        COMPANY_LOGO: 'Company logo image for account',
        COMMERCIAL_REGISTER: 'Commercial register photo',
        TAX_CARD: 'Tax card image',
        SKIP_FOR_NOW: 'Skip for now',
        NEXT: 'Next'
      },
      DEVELOPER_REGISTRATION: {
        TITLE: 'Developer Registration',
        BASIC_INFO: 'Enter Your Basic Information',
        COMPANY_NAME: 'Company Name',
        COMPANY_NAME_PLACEHOLDER: 'Real Estate Development Company',
        COMPANY_EMAIL_PHONE: 'Company email or phone number',
        UPLOAD_DOCUMENTS: 'Please Upload Required Documents',
        DOCUMENTS_DESC: 'You can upload the required documents now or skip and add them later when you first use the required services',
        COMPANY_LOGO: 'Company logo image for account',
        COMMERCIAL_REGISTER: 'Commercial register photo',
        TAX_CARD: 'Tax card image',
        COMPANY_PROFILE: 'Company Profile',
        COMPANY_PROFILE_DESC: 'Company profile and business information',
        SKIP_FOR_NOW: 'Skip for now',
        NEXT: 'Next',
        CHOOSE_MAIN_AREAS: 'Choose main governorates/areas',
        CHOOSE_SUB_AREAS: 'Choose specific sub-areas',
        CAIRO: 'Cairo',
        GIZA: 'Giza',
        ALEXANDRIA: 'Alexandria',
        MAADI: 'Maadi',
        ZAMALEK: 'Zamalek',
        HELIOPOLIS: 'Heliopolis',
        ACCOUNT_DETAILS: 'Enter Your Account Details',
        PHONE: 'Phone',
        EMAIL: 'Email',
        PASSWORD: 'Password',
        CONFIRM_PASSWORD: 'Confirm Password',
        AGREE_TERMS: 'I agree to the Terms and Conditions',
        CREATE_ACCOUNT: 'Create Account',
        CREATING_ACCOUNT: 'Creating Account...'
      }
    },
    PROFILE: {
      LOADING: 'Loading...',
      LOADING_PROFILE: 'Loading Profile...',
      CHANGE_PROFILE_PICTURE: 'Change Profile Picture',
      UPGRADE_PLAN: 'Upgrade Plan',
      SPECIALIZATIONS: 'Specializations',
      LOCATIONS: 'Locations',
      OPERATIONS: 'Operations',
      ADVERTISEMENTS: 'Advertisements',
      PROFILE_IMAGE_UPDATED_SUCCESS: 'Profile image updated successfully',
      PROFILE_IMAGE_UPDATE_FAILED: 'Failed to update profile image',
      PROFILE_UPDATED_SUCCESS: 'Profile updated successfully',
      PROFILE_UPDATE_FAILED: 'Failed to update profile',
      PROFILE_DETAILS: {
        TITLE: 'Profile Details',
        FULL_NAME: 'Full Name',
        FULL_NAME_PLACEHOLDER: 'Full name',
        FULL_NAME_REQUIRED: 'Full name is required.',
        PHONE_NUMBER: 'Phone Number',
        PHONE_NUMBER_PLACEHOLDER: 'Phone number',
        PHONE_NUMBER_REQUIRED: 'Phone number is required.',
        PHONE_NUMBER_PATTERN: 'Phone number must be a valid Egyptian number.',
        PHONE_NUMBER_LENGTH: 'Phone number must be 11 digits.',
        SAVE_CHANGES: 'Save Changes'
      },
      SIGN_IN_METHOD: {
        TITLE: 'Sign-in Method',
        EMAIL_ADDRESS: 'Email Address',
        ENTER_NEW_EMAIL: 'Enter New Email Address',
        EMAIL_PLACEHOLDER: 'Email Address',
        CONFIRM_PASSWORD: 'Confirm Password',
        PASSWORD_REQUIRED: 'Password is required',
        UPDATE_EMAIL: 'Update Email',
        CHANGE_EMAIL: 'Change Email',
        CANCEL: 'Cancel',
        PLEASE_WAIT: 'Please wait...',
        EMAIL_REQUIRED: 'Email address is required.',
        EMAIL_PATTERN: 'Please enter a valid email format (e.g. <EMAIL>).',
        PASSWORD: 'Password',
        RESET_PASSWORD: 'Reset Password',
        NEW_PASSWORD: 'New Password',
        CONFIRM_NEW_PASSWORD: 'Confirm New Password',
        PASSWORD_REQUIREMENTS: 'Password must be at least 8 character and contain symbols',
        UPDATE_PASSWORD: 'Update Password',
        EMAIL_CANNOT_BE_EMPTY: 'Email cannot be empty',
        EMAIL_UPDATED_SUCCESS: 'Email updated successfully',
        EMAIL_UPDATE_FAILED: 'Failed to update email',
        ENTER_ALL_PASSWORD_FIELDS: 'Please enter all password fields',
        PASSWORDS_DO_NOT_MATCH: 'Passwords do not match',
        PASSWORD_UPDATED_SUCCESS: 'Password updated successfully',
        PASSWORD_UPDATE_FAILED: 'Failed to update password'
      },
      ACCOUNT_DETAILS: {
        TITLE: 'Account Details',
        REGISTRATION_PAPERS: 'Registration papers',
        PAYMENTS_RENEWALS: 'Payments and Renewals'
      },
      ADVERTISEMENTS_DETAILS: {
        TITLE: 'Advertisements and Properties Details',
        SPECIALIZATIONS: 'Specializations',
        LOCATIONS: 'Locations',
        LOCATIONS_TOOLTIP: 'Areas can be changed after 17 days',
        ADD_NEW_LOCATION: 'Add New Location',
        CITY: 'City',
        LOADING_CITIES: 'Loading cities...',
        NO_CITIES: 'No cities available',
        SELECT_CITY: 'Select City',
        AREA: 'Area',
        SELECT_CITY_FIRST: 'Please select a city first',
        SELECT_AREA: 'Select Area',
        NO_AREAS_AVAILABLE: 'No areas available. Please select a city first.',
        NO_AREAS_FOR_CITY: 'No areas available for this city',
        SAVE_LOCATION: 'Save Location',
        AVAILABLE_CITIES: 'Available Cities',
        NO_CITIES_AVAILABLE: 'No cities available',
        CANCEL: 'Cancel',
        SELECT_SPECIALIZATIONS: 'Select Specializations',
        CHOOSE_SPECIALIZATION_TREE: 'Choose from the specialization tree:',
        SEARCH_SPECIALIZATIONS: 'Search specializations...',
        INDUSTRIAL: 'Industrial',
        COMMERCIAL: 'Commercial',
        RESIDENTIAL: 'Residential',
        NATIONAL_PROJECT: 'National Project',
        SELECT_SCOPES_TYPES: 'Select scopes and types. Selecting a scope will select all types under it.',
        SAVE: 'Save'
      },
      REGISTRATION_PAPERS: {
        TITLE: 'Registration Papers',
        ADD_DOCUMENT: 'Add Document',
        EDIT_DOCUMENT: 'Edit Document',
        UPLOAD_DOCUMENT: 'Upload Document',
        UPDATE_DOCUMENT: 'Update',
        UPLOAD: 'Upload',
        EDIT: 'Edit',
        CANCEL: 'Cancel',
        SELECT_DOCUMENT: 'Select Document',
        REPLACE_DOCUMENT: 'Replace Document (optional)',
        PENDING_REVIEW: 'Pending review',
        NO_DOCUMENTS: 'No Documents Uploaded',
        NO_DOCUMENTS_MESSAGE: 'Click "Add Document" to upload your first one.',
        BACK_TO_PROFILE: 'Back to Profile',
        CHANGES_REVIEW_MESSAGE: 'Changes will be reviewed by admin.',
        UPLOAD_REVIEW_MESSAGE: 'Uploaded documents will be reviewed by admin.',
        CURRENT_FILE_REMAINS: 'Current file remains',
        STATUS: {
          PENDING: 'Pending',
          APPROVED: 'Approved',
          REJECTED: 'Rejected'
        }
      }
    },
    CITIES: {
      CAIRO: 'Cairo',
      GIZA: 'Giza',
      ALEXANDRIA: 'Alexandria',
      LUXOR: 'Luxor',
      ASWAN: 'Aswan',
      SHARM_EL_SHEIKH: 'Sharm El Sheikh',
      HURGHADA: 'Hurghada',
      MANSOURA: 'Mansoura',
      TANTA: 'Tanta',
      ISMAILIA: 'Ismailia',
      SUEZ: 'Suez',
      PORT_SAID: 'Port Said',
      DAMIETTA: 'Damietta',
      KAFR_EL_SHEIKH: 'Kafr El Sheikh',
      BENI_SUEF: 'Beni Suef',
      MINYA: 'Minya',
      ASYUT: 'Asyut',
      SOHAG: 'Sohag',
      QENA: 'Qena',
      RED_SEA: 'Red Sea',
      NEW_VALLEY: 'New Valley',
      MATROUH: 'Matrouh',
      NORTH_SINAI: 'North Sinai',
      SOUTH_SINAI: 'South Sinai',
      FAYYUM: 'Fayyum',
      BEHEIRA: 'Beheira',
      DAKAHLIA: 'Dakahlia',
      SHARQIA: 'Sharqia',
      MONUFIA: 'Monufia',
      GHARBIA: 'Gharbia',
      QALYUBIA: 'Qalyubia',
      '10TH_OF_RAMADAN': '10th of Ramadan',
      'BADR': 'Badr',
      'NEW_CAIRO': 'New Cairo',
      'SHEIKH_ZAYED': 'Sheikh Zayed',
      'NEW_ADMINISTRATIVE_CAPITAL': 'New Administrative Capital',
      'AL_AZBAKEYAH': 'Al Azbakeyah'
    },
    MONTHS: {
      JANUARY: 'January',
      FEBRUARY: 'February',
      MARCH: 'March',
      APRIL: 'April',
      MAY: 'May',
      JUNE: 'June',
      JULY: 'July',
      AUGUST: 'August',
      SEPTEMBER: 'September',
      OCTOBER: 'October',
      NOVEMBER: 'November',
      DECEMBER: 'December'
    },
    HOME: {
      NAVIGATION: {
        HOME: 'Home',
        ABOUT_US: 'About Us',
        ADVERTISEMENTS: 'Advertisements',
        CONTACT_US: 'Contact Us',
        REGISTER_GUEST: 'Register Guest',
        LANGUAGE: 'English',
        ARABIC: 'العربية'
      },
      HERO: {
        EASY: 'Easy',
        SPEED: 'Speed',
        RELIABILITY: 'Reliability'
      },
      PROPERTIES: {
        TITLE: 'Featured Properties',
        LOADING: 'Loading...',
        LOAD_MORE: 'Load More Properties',
        ROOMS: 'Rooms',
        BATHROOMS: 'Bathrooms',
        AREA: 'Area',
        TYPE: 'Type',
        RATING: '5.0'
      },
      ALL_PROPERTIES: {
        TITLE: 'All Properties',
        SUBTITLE: 'Discover your perfect property from our extensive collection',
        BACK_TO_HOME: 'Back to Home',
        SEARCH_PLACEHOLDER: 'Search properties by type, location, or features...',
        VIEW_DETAILS: 'View Details',
        LOAD_MORE: 'Load More Properties',
        LOADING_MORE: 'Loading more...',
        ALL_LOADED: 'You\'ve seen all available properties!',
        NO_PROPERTIES_FOUND: 'No Properties Found',
        NO_PROPERTIES_MESSAGE: 'No properties match your search criteria. Try adjusting your search terms.',
        NO_PROPERTIES_AVAILABLE: 'No Properties Available',
        NO_PROPERTIES_AVAILABLE_MESSAGE: 'Sorry, we couldn\'t find any properties at the moment.',
        LOGIN_REQUIRED: 'Please Login',
        LOGIN_MESSAGE: 'You need to login to view property details',
        LOGIN_ROLE_MESSAGE: 'You need to have a valid role to view property details',
        INVALID_SESSION: 'Invalid session. Please login again',
        LOGIN_BUTTON: 'Login',
        CANCEL_BUTTON: 'Cancel'
      },
      LOCATIONS: {
        TITLE: 'Explore Locations',
        PROPERTIES_AVAILABLE: 'Properties Available'
      },
      ARTICLES: {
        TITLE: 'Articles That Interest You',
        ALL_ARTICLES: 'All Articles',
        ARTICLE_1: {
          TITLE: 'Modern Finishing Materials - Shop with the Best',
          DESCRIPTION: 'A very quiet area away from the noise and hustle of the city, suitable for large and small families, spacious area with a private garden.'
        },
        ARTICLE_2: {
          TITLE: 'Invest Your Money with Hotel Property',
          DESCRIPTION: 'Excellent investment opportunity in the heart of the city, guaranteed returns and integrated management, strategic location near the airport and commercial centers.'
        },
        ARTICLE_3: {
          TITLE: 'Villa 6 October April 2019',
          DESCRIPTION: 'Latest international finishing materials, high quality and competitive prices, specialized team to implement finishing works to the highest standards.'
        },
        ARTICLE_4: {
          TITLE: 'Apartment in New Cairo',
          DESCRIPTION: 'Modern apartment in the finest neighborhoods of New Cairo, luxury finishes and integrated facilities, close to universities and international schools.'
        },
        ARTICLE_5: {
          TITLE: 'North Coast Properties',
          DESCRIPTION: 'Residential units directly on the sea, wonderful panoramic view, integrated recreational facilities and suitable for summer investment.'
        },
        ARTICLE_6: {
          TITLE: 'Administrative Offices Downtown',
          DESCRIPTION: 'Modern office spaces in the heart of Cairo, suitable for companies and institutions, parking and integrated service facilities.'
        },
        ARTICLE_7: {
          TITLE: 'Luxury Villas in Sheikh Zayed',
          DESCRIPTION: 'Exclusive villas with modern design, private gardens, and premium finishes in the heart of Sheikh Zayed City.'
        },
        ARTICLE_8: {
          TITLE: 'Commercial Spaces in Maadi',
          DESCRIPTION: 'Prime commercial locations in Maadi with high foot traffic, perfect for retail businesses and restaurants.'
        },
        ARTICLE_9: {
          TITLE: 'Investment Opportunities in Heliopolis',
          DESCRIPTION: 'High-yield investment properties in Heliopolis with guaranteed rental income and strategic locations.'
        }
      },
      PROPERTY_OPERATIONS: {
        SELL: 'Sell',
        PURCHASING: 'Purchasing',
        RENT_IN: 'Rent In',
        RENT_OUT: 'Rent Out'
      },
      CURRENCY: {
        EGP: 'EGP'
      },
      UNITS: {
        SQM: 'sqm'
      },
      PROPERTY_TYPES: {
        APARTMENT: 'Apartment',
        DUPLEX: 'Duplex',
        PENTHOUSE: 'Penthouse',
        ROOF: 'Roof',
        STUDIO: 'Studio',
        BASEMENT: 'Basement',
        'FULL BUILDING': 'Full Building',
        'FULL VILLA': 'Full Villa',
        'ADMINISTRATIVE UNIT': 'Administrative Unit',
        'MEDICAL CLINIC': 'Medical Clinic',
        PHARMACY: 'Pharmacy',
        'COMMERCIAL SHOP': 'Commercial Shop',
        'RESIDENTIAL LAND': 'Residential Land',
        'COMMERCIAL ADMINISTRATIVE LAND': 'Commercial Administrative Land',
        'ADMINISTRATIVE BUILDING': 'Administrative Building',
        'COMMERCIAL BUILDING': 'Commercial Building',
        'ADMINISTRATIVE COMMERCIAL BUILDING': 'Administrative Commercial Building',
        FACTORY: 'Factory',
        'INDUSTRIAL LAND': 'Industrial Land',
        WAREHOUSE: 'Warehouse',
        'WAREHOUSE LAND': 'Warehouse Land',
        'I-VILLA': 'I-Villa',
        'TWIN HOUSE': 'Twin House',
        'TOWN HOUSE': 'Town House',
        'STANDALONE VILLA': 'Standalone Villa',
        'SUMMER CHALET': 'Summer Chalet'
      },
      DOWNLOAD_APP: {
        TITLE: 'Download the Electronic App',
        SUBTITLE: 'Download our app to access the latest real estate offers and properties',
        APP_STORE: 'App Store',
        GOOGLE_PLAY: 'Google Play',
        DOWNLOAD_ON: 'Download on the',
        GET_IT_ON: 'GET IT ON',
        APP_NAME: 'EASY DEAL',
        APP_DESCRIPTION: 'Your trusted real estate partner'
      },
      NEWSLETTER: {
        TITLE: 'Join Our Mailing List',
        SUBTITLE: 'Get latest offers',
        PLACEHOLDER: 'Your email',
        SUBSCRIBE: 'Subscribe'
      },
      FOOTER: {
        COMPANY_DESCRIPTION: 'Your trusted real estate partner in Egypt. We provide the best properties and investment opportunities with professional service.',
        QUICK_LINKS: 'Quick Links',
        SERVICES: 'Services',
        CONTACT_INFO: 'Contact Information',
        PHONE: '19888 - <EMAIL>',
        EMAIL: '<EMAIL>',
        ADDRESS: 'Cairo, Egypt',
        COPYRIGHT: '© 2025 EasyDeal. All rights reserved.',
        PRIVACY_POLICY: 'Privacy Policy',
        TERMS_OF_SERVICE: 'Terms of Service',
        COOKIE_POLICY: 'Cookie Policy',
        LINKS: {
          HOME: 'Home',
          ABOUT: 'About EasyDeal',
          NEW_PROJECTS: 'New Projects',
          ADVERTISEMENTS: 'Advertisements',
          CONTACT: 'Contact Us'
        },
        SERVICES_LIST: {
          PROPERTY_SEARCH: 'Property Search',
          INVESTMENT_CONSULTING: 'Investment Consulting',
          PROPERTY_MANAGEMENT: 'Property Management',
          LEGAL_SUPPORT: 'Legal Support',
          FINANCING_OPTIONS: 'Financing Options'
        }
      }
    },
    USER_MENU: {
      DASHBOARD: 'Dashboard',
      MESSAGES: 'Messages',
      MY_PROFILE: 'My Profile',
      NEW_REQUEST: 'New Request',
      REQUESTS: 'Requests',
      HELP: 'Help',
      NOTIFICATIONS: 'Notifications',
      LOGOUT: 'Logout'
    },
    COMMON: {
      ERROR: 'Error!',
      SUCCESS: 'Success!',
      OK: 'OK',
      CANCEL: 'Cancel',
      SAVE: 'Save',
      DELETE: 'Delete',
      EDIT: 'Edit',
      ADD: 'Add',
      BACK: 'Back',
      CLOSE: 'Close',
      CONFIRM: 'Confirm',
      ERRORS: {
        FAILED_TO_LOAD_PROFILE: 'Failed to load profile data.'
      }
    },
    USER_ROLES: {
      ADMIN: 'Admin',
      BROKER: 'Broker',
      CLIENT: 'Client',
      DEVELOPER: 'Developer',
      USER: 'User',
      SUPER_ADMIN: 'Super Admin'
    },
    SUPER_ADMIN: {
      BROKER_DETAILS: {
        TITLE: 'Broker Details',
        SUBTITLE: 'View comprehensive broker information',
        BACK_TO_BROKERS: 'Back to Brokers',
        PROFILE_INFO: 'Profile Information',
        CONTACT_INFO: 'Contact Information',
        ACCOUNT_STATUS: 'Account Status',
        STATISTICS: 'Statistics',
        RECENT_ACTIVITY: 'Recent Activity'
      },
      DEVELOPER_DETAILS: {
        TITLE: 'Developer Details',
        SUBTITLE: 'View comprehensive developer information',
        BACK_TO_DEVELOPERS: 'Back to Developers',
        PROFILE_INFO: 'Profile Information',
        CONTACT_INFO: 'Contact Information',
        ACCOUNT_STATUS: 'Account Status',
        STATISTICS: 'Statistics',
        RECENT_ACTIVITY: 'Recent Activity'
      },
      USER_DETAILS: {
        TITLE: 'User Details',
        SUBTITLE: 'View comprehensive user information',
        BACK_TO_USERS: 'Back to Users',
        PROFILE_INFO: 'Profile Information',
        CONTACT_INFO: 'Contact Information',
        ACCOUNT_STATUS: 'Account Status',
        STATISTICS: 'Statistics',
        RECENT_ACTIVITY: 'Recent Activity'
      },
      DASHBOARD: {
        TITLE: 'Super Admin Dashboard',
        SUBTITLE: 'Comprehensive analytics and management system',
        SYSTEM_ACTIVE: 'System Active',
        GENERAL_DATA: {
          TITLE: 'General Data',
          DESCRIPTION: 'Overview of system-wide analytics',
          EXPLORE: 'Explore Dashboard'
        },
        DEVELOPERS_DATA: {
          TITLE: 'Developers Data',
          DESCRIPTION: 'Manage developer accounts and permissions',
          EXPLORE: 'Explore Dashboard'
        },
        BROKERS_DATA: {
          TITLE: 'Brokers Data',
          DESCRIPTION: 'Monitor broker activities and performance',
          EXPLORE: 'Explore Dashboard'
        },
        CLIENTS_DATA: {
          TITLE: 'Clients Data',
          DESCRIPTION: 'Track client engagement and statistics',
          EXPLORE: 'Explore Dashboard'
        },
        ANALYSIS_CARD: {
          TOTAL_NUMBER: 'Total Number',
          ACTIVE: 'Active'
        },


        BROKERS: {
          TITLE: 'Brokers Data',
          DESCRIPTION: 'Comprehensive statistics for brokers, commissions and transactions',
          REFRESH_DATA: 'Refresh Data',
          ALL_BROKERS: 'All Brokers',
          COMPLAINTS: 'Complaints',
          EXPIRED_SUBSCRIPTIONS: 'Expired Subscriptions',
          EXPIRE_WITHIN_MONTH: 'Expire within a Month',
          TOTAL_UNITS: 'Total Units',
          SOLD_UNITS: 'Sold Units',
          MONTHLY_REQUESTS: 'Monthly Requests',
          MONTH: 'Month',
          SPECIALIZATION: 'Specialization',
          UNIT_TYPE: 'Unit Type',
          COUNT: 'Count',
          COMPANY_BROKERS: 'Company Brokers',
          FREELANCE_BROKERS: 'Freelance Brokers',
          SUMMARY: 'Summary',
          NEW_SUBSCRIPTIONS: 'New Subscriptions',
          DATE: 'Date',
          SUBSCRIPTIONS: 'Subscriptions',
          VALUE: 'Value'
        },
        CLIENTS: {
          TITLE: 'Clients Data',
          DESCRIPTION: 'Comprehensive statistics for clients, requests and behavior',
          REFRESH_DATA: 'Refresh Data',
          TOTAL_CLIENTS: 'Total Clients',
          COMPLAINTS: 'Complaints',
          TOTAL_REQUESTS: 'Total Requests',
          TOTAL_VISITED: 'Total Visited',
          ACTIVE_REQUESTS: 'Active Requests',
          REQUEST_TYPES: 'Request Types',
          MOST_REQUESTED_UNITS: 'Most Requested Units',
          REGISTERED_MONTHLY_ACTIVITY: 'Registered Monthly Activity',
          VISITED_MONTHLY_ACTIVITY: 'Visited Monthly Activity',
          REQUEST_TYPE: 'Request Type',
          TOTAL: 'Total',
          PERCENTAGE: 'Percentage',
          AREA: 'Area',
          TYPE: 'Type',
          COUNT: 'Count',
          MONTH: 'Month',
          NUMBER_OF_CLIENTS: 'Number of Clients',
          NUMBER_OF_VISITORS: 'Number of Visitors',
          NO_OF_CLIENTS: 'No Of Clients',
          NO_OF_VISITORS: 'No Of Visitors'
        },
        GENERAL: {
          TITLE: 'General System Data',
          DESCRIPTION: 'Comprehensive statistics for units, advertisements and prices',
          REFRESH_DATA: 'Refresh Data',
          CLIENTS: 'Clients',
          BROKERS: 'Brokers',
          DEVELOPERS: 'Developers',
          ADMINS: 'Admins',
          MOST_REQUESTED_UNITS: 'Most Requested Units',
          MOST_SOLD_UNITS: 'Most Sold Units',
          MONTHLY_ADVERTISEMENTS: 'Monthly Advertisements',
          REGISTERED_AREAS_SPECIALIZATIONS: 'Registered Areas & Specializations',
          SUBSCRIPTIONS_ACCOUNTS: 'Subscriptions Accounts',
          AVG_UNIT_PRICE: 'Avg Unit Price',
          AREA: 'Area',
          UNIT_TYPE: 'Unit Type',
          COUNT: 'Count',
          DATE: 'Date',
          TOTAL_NUMBER: 'Total Number',
          SPECIALIZATION: 'Specialization',
          ACCOUNT_TYPE: 'Account Type',
          AVG_UNIT_PRICE_LABEL: 'Avg Unit Price',
          AVG_UNIT_PRICE_VALUE: 'Avg Unit Price'
        },
        DEVELOPERS: {
          TITLE: 'Developers Data',
          DESCRIPTION: 'Comprehensive statistics for developers, projects and average payment duration',
          REFRESH_DATA: 'Refresh Data',
          TOTAL_DEVELOPERS: 'Total Developers',
          COMPLAINTS: 'Complaints',
          TOTAL_UNITS: 'Total Units',
          TOTAL_SOLD_UNITS: 'Total Sold Units',
          TOTAL_PROJECTS: 'Total Projects',
          TOP_SALE_PROJECTS: 'Top Sale Projects',
          TOP_SALE_MODELS: 'Top Sale Models',
          PROJECT_NAME: 'Project Name',
          PROJECT_DESIGNER: 'Project Designer',
          MODEL_CODE: 'Model Code',
          SOLD_UNITS_COUNT: 'Sold Units Count',
          AVG_UNIT_PRICE_PER_METER: 'Avg Unit Price per Meter',
          AREA: 'Area',
          UNIT_TYPE: 'Unit Type',
          AVG_PRICE_PER_METER: 'Avg Price per Meter'
        },
        CONTRACT_REQUESTS: {
          TITLE: 'Contract Requests',
          DESCRIPTION: 'Analysis of contract requests',
          PENDING: 'Pending',
          ACCEPTED: 'Accepted',
          DECLINED: 'Declined'
        }
      },
      ALL_DEVELOPERS: {
        TITLE: 'All Developers',
        SEARCH_PLACEHOLDER: 'Search By Developer Name...',
        FILTER: 'Filter',
        ADD_DEVELOPER: 'Add Developer',
        TABLE: {
          DEVELOPER: 'Developer',
          EMAIL: 'Email',
          PHONE: 'Phone',
          NO_OF_PROJECTS: 'No Of Projects',
          PROJECTS: 'Projects',
          ACTIVE: 'Active',
          ACTIONS: 'Actions'
        },
        VIEW_DETAILS: 'View Details'
      },
      ALL_BROKERS: {
        TITLE: 'All Brokers',
        SEARCH_PLACEHOLDER: 'Search By Broker Name...',
        FILTER: 'Filter',
        ADD_BROKER: 'Add Broker',
        TABLE: {
          BROKER: 'Broker',
          EMAIL: 'Email',
          PHONE: 'Phone',
          ADVERTISEMENT_COUNT: 'Advertisement Count',
          RATING: 'Rating',
          ACTIVE: 'Active',
          ACTIONS: 'Actions'
        },
        VIEW_DETAILS: 'View Details'
      },
      ALL_USERS: {
        TITLE: 'All Users',
        SEARCH_PLACEHOLDER: 'Search By User Name...',
        FILTER: 'Filter',
        ADD_USER: 'Add User',
        TABLE: {
          USER: 'User',
          EMAIL: 'Email',
          PHONE: 'Phone',
          USER_TYPE: 'User Type',
          VERIFIED: 'Verified',
          ACTIVE: 'Active',
          ACTIONS: 'Actions'
        },
        VIEW_DETAILS: 'View Details',
        VERIFIED: 'Verified',
        NOT_VERIFIED: 'Not Verified'
      },
      SETTINGS: {
        TITLE: 'System Settings & Administration',
        ADVANCED_SETTINGS: 'Advanced Settings',
        FEATURES: {
          USER_MANAGEMENT: {
            TITLE: 'User Management',
            DESCRIPTION: 'Manage user accounts and permissions'
          },
          SYSTEM_SETTINGS: {
            TITLE: 'System Settings',
            DESCRIPTION: 'Configure general system settings'
          },
          PERMISSIONS: {
            TITLE: 'Permissions',
            DESCRIPTION: 'Manage user permissions and roles'
          },
          PACKAGES: {
            TITLE: 'Packages',
            DESCRIPTION: 'Manage subscription packages and services'
          },
          SUBSCRIPTION_FORM: {
            TITLE: 'Subscription Form',
            DESCRIPTION: 'Manage subscription and registration forms'
          },
          USER_ACCOUNT_TYPES: {
            TITLE: 'User Account Types',
            DESCRIPTION: 'Define and manage different account types'
          }
        },
        PACKAGES: {
          TITLE: 'Subscription Management',
          DESCRIPTION: 'Manage subscription plans and pricing',
          CREATE_NEW: 'Create New Subscription',
          EGP: 'EGP',
          MONTHLY: 'Monthly',
          SPECIALIZATIONS: 'Specializations',
          LOCATIONS: 'Locations',
          ADVERTISEMENTS: 'Advertisements',
          OPERATIONS: 'Operations',
          EDIT: 'Edit',
          DELETE: 'Delete'
        }
      },
      COMMON: {
        LOADING: 'Loading...',
        NO_DATA: 'No data available',
        SEARCH: 'Search',
        FILTER: 'Filter',
        ADD: 'Add',
        EDIT: 'Edit',
        DELETE: 'Delete',
        VIEW: 'View',
        SAVE: 'Save',
        CANCEL: 'Cancel',
        CONFIRM: 'Confirm',
        YES: 'Yes',
        NO: 'No',
        ACTIVE: 'Active',
        INACTIVE: 'Inactive',
        STATUS: 'Status',
        ACTIONS: 'Actions',
        DETAILS: 'Details',
        BACK: 'Back',
        NEXT: 'Next',
        PREVIOUS: 'Previous',
        FIRST: 'First',
        LAST: 'Last',
        PAGE: 'Page',
        OF: 'of',
        ITEMS_PER_PAGE: 'Items per page',
        TOTAL_ITEMS: 'Total items'
      }
    },
    ACCOUNT_TYPES: {
      FREE: 'Free',
      PREMIUM: 'Premium',
      BASIC: 'Basic',
      GOLD: 'Gold',
      GOLDEN: 'Golden',
      SILVER: 'Silver',
      BRONZE: 'Bronze',
      'GOLDEN ACCOUNT': 'Golden Account',
      'SILVER ACCOUNT': 'Silver Account',
      'BRONZE ACCOUNT': 'Bronze Account',
      'PURCHASING SELL RESIDENTIAL OUTSIDE COMPOUND': 'Purchasing Sell Residential Outside Compound',
      'PURCHASING SELL ADMINISTRATIVE COMMERCIAL UNITS OUTSIDE COMPOUND': 'Purchasing Sell Administrative Commercial Units Outside Compound'
    },
    USER_ACCOUNT_TYPES: {
      TITLE: 'User Account Types',
      SUBTITLE: 'Manage users, permissions, and roles',
      PERMISSIONS_CONTROL: {
        TITLE: 'Permissions Control',
        DESCRIPTION: 'Define and manage system permissions and access rights'
      },
      ROLES_MANAGEMENT: {
        TITLE: 'Roles Management',
        DESCRIPTION: 'Create and assign user roles with specific permissions'
      },
      USERS_MANAGEMENT: {
        TITLE: 'Users Management',
        DESCRIPTION: 'Manage system users and their information'
      }
    },
    PERMISSIONS: {
      TITLE: 'Permissions Control',
      SUBTITLE: 'Manage system permissions and access rights',
      ALL_PERMISSIONS: 'All Permissions',
      SEARCH_PLACEHOLDER: 'Search permissions...',
      PERMISSION_DETAILS: 'Permission Details',
      CREATED_DATE: 'Created Date',
      ADD_PERMISSION: 'Add Permission',
      NO_PERMISSIONS_FOUND: 'No permissions found',
      NO_PERMISSIONS_MATCH: 'No permissions match your search',
      NO_PERMISSIONS_DESCRIPTION: 'Create your first permission to get started with access control',
      ADD_NEW_PERMISSION: 'Add New Permission',
      PERMISSION_NAME: 'Permission Name',
      DESCRIPTION: 'Description',
      CREATE: 'Create',
      PERMISSION_NAME_REQUIRED: 'Permission name is required!',
      SUCCESS: 'Success!',
      PERMISSION_CREATED_SUCCESS: 'Permission created successfully!',
      ERROR: 'Error!',
      PERMISSION_CREATE_FAILED: 'Failed to create permission.',
      LOAD_PERMISSIONS_FAILED: 'Failed to load permissions. Please try again.',
      BACK: 'Back'
    },
    ROLES: {
      TITLE: 'Roles Management',
      SUBTITLE: 'Create and assign user roles with specific permissions',
      ALL_ROLES: 'All Roles',
      SEARCH_PLACEHOLDER: 'Search roles...',
      ROLE: 'Role',
      CREATED_DATE: 'Created Date',
      ACTIONS: 'Actions',
      ADD_ROLE: 'Add Role',
      NO_ROLES_FOUND: 'No roles found',
      NO_ROLES_MATCH: 'No roles match your search',
      PERMISSIONS: 'Permissions',
      ADD_NEW_ROLE: 'Add New Role',
      ROLE_NAME: 'Role Name',
      ENTER_ROLE_NAME: 'Enter role name...',
      CREATE_ROLE: 'Create Role',
      CANCEL: 'Cancel',
      ROLE_NAME_REQUIRED: 'Role name is required!',
      SUCCESS: 'Success!',
      ROLE_CREATED_SUCCESS: 'Role created successfully!',
      ERROR: 'Error!',
      ROLE_CREATE_FAILED: 'Failed to create role. Please try again.',
      LOAD_ROLES_FAILED: 'Failed to load roles. Please try again.',
      ROLE_PERMISSIONS: 'Role Permissions',
      SELECT_ALL_PERMISSIONS: 'Select All Permissions',
      NO_PERMISSIONS_FOUND: 'No permissions found',
      SAVE_CHANGES: 'Save Changes',
      PERMISSIONS_UPDATED_SUCCESS: 'Permissions updated successfully!',
      PERMISSIONS_UPDATE_FAILED: 'Error updating permissions!',
      BACK: 'Back',
      ROLE_DESCRIPTION: 'System role with specific permissions',
      NO_ROLES_DESCRIPTION: 'Create your first role to get started with user management'
    },
    USERS: {
      TITLE: 'Users Management',
      SUBTITLE: 'Manage system users and their information',
      ALL_USERS: 'All Users',
      SEARCH_PLACEHOLDER: 'Search users...',
      USER: 'User',
      ROLE: 'Role',
      STATUS: 'Status',
      LAST_LOGIN: 'Last Login',
      ACTIONS: 'Actions',
      ADD_USER: 'Add User',
      EDIT: 'Edit',
      DELETE: 'Delete',
      ALL_ROLES: 'All Roles',
      ADMIN: 'Admin',
      BROKER: 'Broker',
      DEVELOPER: 'Developer',
      CLIENT: 'Client',
      SUPER_ADMIN: 'Super Admin',
      ACTIVE: 'Active',
      INACTIVE: 'Inactive',
      PENDING: 'Pending',
      DELETE_USER_CONFIRM: 'Are you sure you want to delete this user?',
      BACK: 'Back'
    },
    SUBSCRIPTION: {
      TITLE: 'Subscription Management',
      CREATE_TITLE: 'Create New Subscription',
      EDIT_TITLE: 'Edit Subscription',
      BASIC_INFORMATION: 'Basic Information',
      SUBSCRIPTION_NAME: 'Subscription Name',
      ENTER_SUBSCRIPTION_NAME: 'Enter subscription name',
      DESCRIPTION: 'Description',
      ENTER_SUBSCRIPTION_DESCRIPTION: 'Enter subscription description',
      PRICE: 'Price',
      SUBSCRIPTION_LIMITS: 'Subscription Limits',
      MAX_SPECIALIZATION_SCOPES: 'Max Specialization Scopes',
      MAX_SPECIALIZATIONS: 'Max Specializations',
      MAX_LOCATIONS: 'Max Locations',
      MAX_ADVERTISEMENTS: 'Max Advertisements',
      MAX_OPERATIONS: 'Max Operations',
      SPECIAL_ADVERTISEMENTS_COUNT: 'Special Advertisements Count',
      SUBSCRIPTION_IMAGE: 'Subscription Image',
      CHOOSE_IMAGE: 'Choose Image',
      NO_FILE_CHOSEN: 'No file chosen',
      BACK_TO_SUBSCRIPTIONS: 'Back to Subscriptions',
      CANCEL: 'Cancel',
      CREATE_SUBSCRIPTION: 'Create Subscription',
      UPDATE_SUBSCRIPTION: 'Update Subscription',
      SAVE: 'Save',
      REQUIRED_FIELD: 'This field is required',
      MIN_VALUE_ERROR: 'Value must be greater than or equal to 0',
      MAX_LENGTH_ERROR: 'Maximum length is 255 characters'
    },
    ECOMMERCE: {
      COMMON: {
        SELECTED_RECORDS_COUNT: 'Selected records count: ',
        ALL: 'All',
        SUSPENDED: 'Suspended',
        ACTIVE: 'Active',
        FILTER: 'Filter',
        BY_STATUS: 'by Status',
        BY_TYPE: 'by Type',
        BUSINESS: 'Business',
        INDIVIDUAL: 'Individual',
        SEARCH: 'Search',
        IN_ALL_FIELDS: 'in all fields'
      },
      ECOMMERCE: 'eCommerce',
      CUSTOMERS: {
        CUSTOMERS: 'Customers',
        CUSTOMERS_LIST: 'Customers list',
        NEW_CUSTOMER: 'New Customer',
        DELETE_CUSTOMER_SIMPLE: {
          TITLE: 'Customer Delete',
          DESCRIPTION: 'Are you sure to permanently delete this customer?',
          WAIT_DESCRIPTION: 'Customer is deleting...',
          MESSAGE: 'Customer has been deleted'
        },
        DELETE_CUSTOMER_MULTY: {
          TITLE: 'Customers Delete',
          DESCRIPTION: 'Are you sure to permanently delete selected customers?',
          WAIT_DESCRIPTION: 'Customers are deleting...',
          MESSAGE: 'Selected customers have been deleted'
        },
        UPDATE_STATUS: {
          TITLE: 'Status has been updated for selected customers',
          MESSAGE: 'Selected customers status have successfully been updated'
        },
        EDIT: {
          UPDATE_MESSAGE: 'Customer has been updated',
          ADD_MESSAGE: 'Customer has been created'
        }
      }
    }
  }
};
