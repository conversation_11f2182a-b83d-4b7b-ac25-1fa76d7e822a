<div class="container-fluid" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
  <!-- Header -->
  <div class="row mb-3">
    <div class="col-12">
      <div class="card border-0 shadow-sm">
        <div class="card-body">
          <div class="d-flex align-items-center w-100 justify-content-between">

            <!-- في العربية: الزر أولاً (أقصى اليسار) -->
            <div *ngIf="translationService.isRTL()" class="flex-shrink-0 me-3">
              <button class="btn btn-success btn-sm" (click)="refresh()"
                [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                <i class="ki-duotone ki-arrows-circle fs-5 ms-2">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
                {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.REFRESH_DATA' | translate }}
              </button>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="d-flex align-items-center flex-grow-1">
              <div class="symbol symbol-40px" [class.me-3]="!translationService.isRTL()"
                [class.ms-3]="translationService.isRTL()">
                <div class="symbol-label bg-light-primary">
                  <i class="ki-duotone ki-office-bag fs-3 text-primary">
                    <span class="path1"></span>
                    <span class="path2"></span>
                    <span class="path3"></span>
                    <span class="path4"></span>
                  </i>
                </div>
              </div>
              <div class="flex-grow-1" [class.text-start]="!translationService.isRTL()"
                [class.text-end]="translationService.isRTL()">
                <h1 class="text-gray-900 fw-bold mb-1 fs-2"
                  [style.font-family]="translationService.isRTL() ? 'Noto Kufi Arabic, sans-serif' : 'inherit'">
                  {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.TITLE' | translate }}
                </h1>
                <p class="text-muted mb-0 fs-6"
                  [style.font-family]="translationService.isRTL() ? 'Hacen Liner Screen, sans-serif' : 'inherit'">
                  {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.DESCRIPTION' | translate }}
                </p>
              </div>
            </div>

            <!-- في الإنجليزية: الزر آخراً (أقصى اليمين) -->
            <div *ngIf="!translationService.isRTL()" class="flex-shrink-0">
              <button class="btn btn-success btn-sm" (click)="refresh()">
                <i class="ki-duotone ki-arrows-circle fs-5 me-2">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
                {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.REFRESH_DATA' | translate }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Statistics Cards -->
  <div class="row g-4 mb-5">
    <div class="col-lg-3 col-md-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol" [class.symbol-60px]="!translationService.isRTL()"
              [class.symbol-45px]="translationService.isRTL()" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-primary">
                <i class="ki-duotone ki-office-bag text-primary" [class.fs-2]="!translationService.isRTL()"
                  [class.fs-3]="translationService.isRTL()">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                  <span class="path4"></span>
                </i>
              </div>
            </div>
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
              <div class="fs-2 fw-bold text-gray-900 mb-1">
                {{ formatNumber(totalDevelopers) | arabicNumbers }}
              </div>
              <div class="fs-7 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.TOTAL_DEVELOPERS' | translate }}
              </div>
              <div class="fs-8 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ totalComplaints | arabicNumbers }} {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.COMPLAINTS' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol symbol-60px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-warning">
                <i class="ki-duotone ki-element-11 fs-2 text-warning">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                  <span class="path4"></span>
                </i>
              </div>
            </div>
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
              <div class="fs-2 fw-bold text-gray-900 mb-1">
                {{ formatNumber(totalProjects) }}
              </div>
              <div class="fs-7 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.TOTAL_PROJECTS' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol symbol-60px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-info">
                <i class="ki-duotone ki-home fs-2 text-info">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
              </div>
            </div>
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
              <div class="fs-2 fw-bold text-gray-900 mb-1">
                {{ formatNumber(totalUnits) }}
              </div>
              <div class="fs-7 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.TOTAL_UNITS' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-3 col-md-6">
      <div class="card border-0 shadow-sm h-100">
        <div class="card-body p-4">
          <div class="d-flex align-items-center" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <div class="symbol" [class.symbol-60px]="!translationService.isRTL()"
              [class.symbol-45px]="translationService.isRTL()" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-success">
                <i class="ki-duotone ki-check-circle text-success" [class.fs-2]="!translationService.isRTL()"
                  [class.fs-3]="translationService.isRTL()">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
              </div>
            </div>
            <div [class.text-end]="translationService.isRTL()" [class.text-start]="!translationService.isRTL()">
              <div class="fs-2 fw-bold text-gray-900 mb-1">
                {{ formatNumber(totalSoldUnits) | arabicNumbers }}
              </div>
              <div class="fs-7 text-muted"
                [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.TOTAL_SOLD_UNITS' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Projects & Models -->
  <div class="row g-4 mb-5" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
    <!-- Top Sale Projects -->
    <div class="col-lg-6">
      <div class="card border-0 shadow-sm h-100" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
        <div class="card-header border-0 py-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="d-flex align-items-center w-100" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
            [style.justify-content]="translationService.isRTL() ? 'flex-end !important' : 'flex-start !important'"
            [class.justify-content-end-rtl]="translationService.isRTL()"
            [class.justify-content-start-ltr]="!translationService.isRTL()">
            <div class="symbol symbol-40px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-success">
                <i class="ki-duotone ki-element-11 fs-3 text-success">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                  <span class="path4"></span>
                </i>
              </div>
            </div>
            <h3 class="card-title text-gray-900 fw-bold fs-4 mb-0"
              [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.TOP_SALE_PROJECTS' | translate }}
            </h3>
          </div>
        </div>
        <div class="card-body p-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="table-responsive" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <table class="table table-hover align-middle" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
              [class]="translationService.isRTL() ? 'table-rtl' : 'table-ltr'">
              <thead class="table-light">
                <tr>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.PROJECT_NAME' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.PROJECT_DESIGNER' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.SOLD_UNITS_COUNT' | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let project of topSoldProjects">
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-900 fw-semibold fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ project.projectName }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-700 fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ project.projectDesigner }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="badge badge-light-success fs-7">{{ project.unitSoldCount }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Top Sale Models -->
    <div class="col-lg-6">
      <div class="card border-0 shadow-sm h-100" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
        <div class="card-header border-0 py-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="d-flex align-items-center w-100" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
            [style.justify-content]="translationService.isRTL() ? 'flex-end !important' : 'flex-start !important'"
            [class.justify-content-end-rtl]="translationService.isRTL()"
            [class.justify-content-start-ltr]="!translationService.isRTL()">
            <div class="symbol symbol-40px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-info">
                <i class="ki-duotone ki-home fs-3 text-info">
                  <span class="path1"></span>
                  <span class="path2"></span>
                </i>
              </div>
            </div>
            <h3 class="card-title text-gray-900 fw-bold fs-4 mb-0"
              [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.TOP_SALE_MODELS' | translate }}
            </h3>
          </div>
        </div>
        <div class="card-body p-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="table-responsive" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <table class="table table-hover align-middle" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
              [class]="translationService.isRTL() ? 'table-rtl' : 'table-ltr'">
              <thead class="table-light">
                <tr>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.MODEL_CODE' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.PROJECT_NAME' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.SOLD_UNITS_COUNT' | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let model of topSoldModels">
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-900 fw-semibold fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ model.modelCode }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-700 fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ model.projectName }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="badge badge-light-info fs-7">{{ model.unitsSoldCount }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Unit Price Statistics -->
  <div class="row mb-5" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
    <div class="col-12">
      <div class="card border-0 shadow-sm" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
        <div class="card-header border-0 py-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="d-flex align-items-center w-100" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
            [style.justify-content]="translationService.isRTL() ? 'flex-end !important' : 'flex-start !important'"
            [class.justify-content-end-rtl]="translationService.isRTL()"
            [class.justify-content-start-ltr]="!translationService.isRTL()">
            <div class="symbol symbol-40px" [class.me-3]="!translationService.isRTL()"
              [class.ms-3]="translationService.isRTL()">
              <div class="symbol-label bg-light-primary">
                <i class="ki-duotone ki-chart-simple fs-3 text-primary">
                  <span class="path1"></span>
                  <span class="path2"></span>
                  <span class="path3"></span>
                  <span class="path4"></span>
                </i>
              </div>
            </div>
            <h3 class="card-title text-gray-900 fw-bold fs-3 mb-0"
              [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
              [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
              {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.AVG_UNIT_PRICE_PER_METER' | translate }}
            </h3>
          </div>
        </div>
        <div class="card-body p-4" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
          <div class="table-responsive" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
            <table class="table table-hover align-middle" [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'"
              [class]="translationService.isRTL() ? 'table-rtl' : 'table-ltr'">
              <thead class="table-light">
                <tr>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.AREA' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.UNIT_TYPE' | translate }}
                  </th>
                  <th class="fw-bold text-gray-700 fs-6 py-3"
                    [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'"
                    [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    {{ 'SUPER_ADMIN.DASHBOARD.DEVELOPERS.AVG_PRICE_PER_METER' | translate }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let unit of unitPriceStatistics; let i = index">
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <div class="d-flex align-items-center"
                      [style.direction]="translationService.isRTL() ? 'rtl' : 'ltr'">
                      <div class="symbol symbol-40px" [class.me-3]="!translationService.isRTL()"
                        [class.ms-3]="translationService.isRTL()">
                        <div class="symbol-label bg-light-primary">
                          <span class="text-primary fw-bold">{{ i + 1 }}</span>
                        </div>
                      </div>
                      <span class="text-gray-900 fw-semibold fs-6"
                        [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                        {{ unit.area }}
                      </span>
                    </div>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="text-gray-700 fs-6"
                      [style.font-family]="translationService.isRTL() ? 'Cairo, sans-serif' : 'inherit'">
                      {{ unit.unitType }}
                    </span>
                  </td>
                  <td class="py-3" [style.text-align]="translationService.isRTL() ? 'right' : 'left'">
                    <span class="badge badge-light-primary fs-7">{{ formatNumber(unit.averagePricePermeterInCash)
                      }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>